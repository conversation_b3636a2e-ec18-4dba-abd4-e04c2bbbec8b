{"version": 3, "sources": ["../../@fortawesome/fontawesome-svg-core/index.mjs"], "sourcesContent": ["/*!\n * Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)\n * Copyright 2024 Fonticons, Inc.\n */\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _inherits(t, e) {\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n  t.prototype = Object.create(e && e.prototype, {\n    constructor: {\n      value: t,\n      writable: !0,\n      configurable: !0\n    }\n  }), Object.defineProperty(t, \"prototype\", {\n    writable: !1\n  }), e && _setPrototypeOf(t, e);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _wrapRegExp() {\n  _wrapRegExp = function (e, r) {\n    return new BabelRegExp(e, void 0, r);\n  };\n  var e = RegExp.prototype,\n    r = new WeakMap();\n  function BabelRegExp(e, t, p) {\n    var o = RegExp(e, t);\n    return r.set(o, p || r.get(e)), _setPrototypeOf(o, BabelRegExp.prototype);\n  }\n  function buildGroups(e, t) {\n    var p = r.get(t);\n    return Object.keys(p).reduce(function (r, t) {\n      var o = p[t];\n      if (\"number\" == typeof o) r[t] = e[o];else {\n        for (var i = 0; void 0 === e[o[i]] && i + 1 < o.length;) i++;\n        r[t] = e[o[i]];\n      }\n      return r;\n    }, Object.create(null));\n  }\n  return _inherits(BabelRegExp, RegExp), BabelRegExp.prototype.exec = function (r) {\n    var t = e.exec.call(this, r);\n    if (t) {\n      t.groups = buildGroups(t, this);\n      var p = t.indices;\n      p && (p.groups = buildGroups(p, this));\n    }\n    return t;\n  }, BabelRegExp.prototype[Symbol.replace] = function (t, p) {\n    if (\"string\" == typeof p) {\n      var o = r.get(this);\n      return e[Symbol.replace].call(this, t, p.replace(/\\$<([^>]+)>/g, function (e, r) {\n        var t = o[r];\n        return \"$\" + (Array.isArray(t) ? t.join(\"$\") : t);\n      }));\n    }\n    if (\"function\" == typeof p) {\n      var i = this;\n      return e[Symbol.replace].call(this, t, function () {\n        var e = arguments;\n        return \"object\" != typeof e[e.length - 1] && (e = [].slice.call(e)).push(buildGroups(e, i)), p.apply(this, e);\n      });\n    }\n    return e[Symbol.replace].call(this, t, p);\n  }, _wrapRegExp.apply(this, arguments);\n}\n\nconst noop = () => {};\nlet _WINDOW = {};\nlet _DOCUMENT = {};\nlet _MUTATION_OBSERVER = null;\nlet _PERFORMANCE = {\n  mark: noop,\n  measure: noop\n};\ntry {\n  if (typeof window !== 'undefined') _WINDOW = window;\n  if (typeof document !== 'undefined') _DOCUMENT = document;\n  if (typeof MutationObserver !== 'undefined') _MUTATION_OBSERVER = MutationObserver;\n  if (typeof performance !== 'undefined') _PERFORMANCE = performance;\n} catch (e) {}\nconst {\n  userAgent = ''\n} = _WINDOW.navigator || {};\nconst WINDOW = _WINDOW;\nconst DOCUMENT = _DOCUMENT;\nconst MUTATION_OBSERVER = _MUTATION_OBSERVER;\nconst PERFORMANCE = _PERFORMANCE;\nconst IS_BROWSER = !!WINDOW.document;\nconst IS_DOM = !!DOCUMENT.documentElement && !!DOCUMENT.head && typeof DOCUMENT.addEventListener === 'function' && typeof DOCUMENT.createElement === 'function';\nconst IS_IE = ~userAgent.indexOf('MSIE') || ~userAgent.indexOf('Trident/');\n\nvar p = /fa(s|r|l|t|d|dr|dl|dt|b|k|kd|ss|sr|sl|st|sds|sdr|sdl|sdt)?[\\-\\ ]/,\n  g = /Font ?Awesome ?([56 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp Duotone|Sharp|Kit)?.*/i;\nvar S = {\n    classic: {\n      fa: \"solid\",\n      fas: \"solid\",\n      \"fa-solid\": \"solid\",\n      far: \"regular\",\n      \"fa-regular\": \"regular\",\n      fal: \"light\",\n      \"fa-light\": \"light\",\n      fat: \"thin\",\n      \"fa-thin\": \"thin\",\n      fab: \"brands\",\n      \"fa-brands\": \"brands\"\n    },\n    duotone: {\n      fa: \"solid\",\n      fad: \"solid\",\n      \"fa-solid\": \"solid\",\n      \"fa-duotone\": \"solid\",\n      fadr: \"regular\",\n      \"fa-regular\": \"regular\",\n      fadl: \"light\",\n      \"fa-light\": \"light\",\n      fadt: \"thin\",\n      \"fa-thin\": \"thin\"\n    },\n    sharp: {\n      fa: \"solid\",\n      fass: \"solid\",\n      \"fa-solid\": \"solid\",\n      fasr: \"regular\",\n      \"fa-regular\": \"regular\",\n      fasl: \"light\",\n      \"fa-light\": \"light\",\n      fast: \"thin\",\n      \"fa-thin\": \"thin\"\n    },\n    \"sharp-duotone\": {\n      fa: \"solid\",\n      fasds: \"solid\",\n      \"fa-solid\": \"solid\",\n      fasdr: \"regular\",\n      \"fa-regular\": \"regular\",\n      fasdl: \"light\",\n      \"fa-light\": \"light\",\n      fasdt: \"thin\",\n      \"fa-thin\": \"thin\"\n    }\n  },\n  A = {\n    GROUP: \"duotone-group\",\n    SWAP_OPACITY: \"swap-opacity\",\n    PRIMARY: \"primary\",\n    SECONDARY: \"secondary\"\n  },\n  P = [\"fa-classic\", \"fa-duotone\", \"fa-sharp\", \"fa-sharp-duotone\"];\nvar s = \"classic\",\n  t = \"duotone\",\n  r = \"sharp\",\n  o = \"sharp-duotone\",\n  L = [s, t, r, o];\nvar G = {\n    classic: {\n      900: \"fas\",\n      400: \"far\",\n      normal: \"far\",\n      300: \"fal\",\n      100: \"fat\"\n    },\n    duotone: {\n      900: \"fad\",\n      400: \"fadr\",\n      300: \"fadl\",\n      100: \"fadt\"\n    },\n    sharp: {\n      900: \"fass\",\n      400: \"fasr\",\n      300: \"fasl\",\n      100: \"fast\"\n    },\n    \"sharp-duotone\": {\n      900: \"fasds\",\n      400: \"fasdr\",\n      300: \"fasdl\",\n      100: \"fasdt\"\n    }\n  };\nvar lt = {\n    \"Font Awesome 6 Free\": {\n      900: \"fas\",\n      400: \"far\"\n    },\n    \"Font Awesome 6 Pro\": {\n      900: \"fas\",\n      400: \"far\",\n      normal: \"far\",\n      300: \"fal\",\n      100: \"fat\"\n    },\n    \"Font Awesome 6 Brands\": {\n      400: \"fab\",\n      normal: \"fab\"\n    },\n    \"Font Awesome 6 Duotone\": {\n      900: \"fad\",\n      400: \"fadr\",\n      normal: \"fadr\",\n      300: \"fadl\",\n      100: \"fadt\"\n    },\n    \"Font Awesome 6 Sharp\": {\n      900: \"fass\",\n      400: \"fasr\",\n      normal: \"fasr\",\n      300: \"fasl\",\n      100: \"fast\"\n    },\n    \"Font Awesome 6 Sharp Duotone\": {\n      900: \"fasds\",\n      400: \"fasdr\",\n      normal: \"fasdr\",\n      300: \"fasdl\",\n      100: \"fasdt\"\n    }\n  };\nvar pt = new Map([[\"classic\", {\n    defaultShortPrefixId: \"fas\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\", \"brands\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"sharp\", {\n    defaultShortPrefixId: \"fass\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"duotone\", {\n    defaultShortPrefixId: \"fad\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"sharp-duotone\", {\n    defaultShortPrefixId: \"fasds\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }]]),\n  xt = {\n    classic: {\n      solid: \"fas\",\n      regular: \"far\",\n      light: \"fal\",\n      thin: \"fat\",\n      brands: \"fab\"\n    },\n    duotone: {\n      solid: \"fad\",\n      regular: \"fadr\",\n      light: \"fadl\",\n      thin: \"fadt\"\n    },\n    sharp: {\n      solid: \"fass\",\n      regular: \"fasr\",\n      light: \"fasl\",\n      thin: \"fast\"\n    },\n    \"sharp-duotone\": {\n      solid: \"fasds\",\n      regular: \"fasdr\",\n      light: \"fasdl\",\n      thin: \"fasdt\"\n    }\n  };\nvar Ft = [\"fak\", \"fa-kit\", \"fakd\", \"fa-kit-duotone\"],\n  St = {\n    kit: {\n      fak: \"kit\",\n      \"fa-kit\": \"kit\"\n    },\n    \"kit-duotone\": {\n      fakd: \"kit-duotone\",\n      \"fa-kit-duotone\": \"kit-duotone\"\n    }\n  },\n  At = [\"kit\"];\nvar Ct = {\n  kit: {\n    \"fa-kit\": \"fak\"\n  },\n  \"kit-duotone\": {\n    \"fa-kit-duotone\": \"fakd\"\n  }\n};\nvar Lt = [\"fak\", \"fakd\"],\n  Wt = {\n    kit: {\n      fak: \"fa-kit\"\n    },\n    \"kit-duotone\": {\n      fakd: \"fa-kit-duotone\"\n    }\n  };\nvar Et = {\n    kit: {\n      kit: \"fak\"\n    },\n    \"kit-duotone\": {\n      \"kit-duotone\": \"fakd\"\n    }\n  };\n\nvar t$1 = {\n    GROUP: \"duotone-group\",\n    SWAP_OPACITY: \"swap-opacity\",\n    PRIMARY: \"primary\",\n    SECONDARY: \"secondary\"\n  },\n  r$1 = [\"fa-classic\", \"fa-duotone\", \"fa-sharp\", \"fa-sharp-duotone\"];\nvar bt$1 = [\"fak\", \"fa-kit\", \"fakd\", \"fa-kit-duotone\"];\nvar Yt = {\n    \"Font Awesome Kit\": {\n      400: \"fak\",\n      normal: \"fak\"\n    },\n    \"Font Awesome Kit Duotone\": {\n      400: \"fakd\",\n      normal: \"fakd\"\n    }\n  };\nvar ua = {\n    classic: {\n      \"fa-brands\": \"fab\",\n      \"fa-duotone\": \"fad\",\n      \"fa-light\": \"fal\",\n      \"fa-regular\": \"far\",\n      \"fa-solid\": \"fas\",\n      \"fa-thin\": \"fat\"\n    },\n    duotone: {\n      \"fa-regular\": \"fadr\",\n      \"fa-light\": \"fadl\",\n      \"fa-thin\": \"fadt\"\n    },\n    sharp: {\n      \"fa-solid\": \"fass\",\n      \"fa-regular\": \"fasr\",\n      \"fa-light\": \"fasl\",\n      \"fa-thin\": \"fast\"\n    },\n    \"sharp-duotone\": {\n      \"fa-solid\": \"fasds\",\n      \"fa-regular\": \"fasdr\",\n      \"fa-light\": \"fasdl\",\n      \"fa-thin\": \"fasdt\"\n    }\n  },\n  I$1 = {\n    classic: [\"fas\", \"far\", \"fal\", \"fat\", \"fad\"],\n    duotone: [\"fadr\", \"fadl\", \"fadt\"],\n    sharp: [\"fass\", \"fasr\", \"fasl\", \"fast\"],\n    \"sharp-duotone\": [\"fasds\", \"fasdr\", \"fasdl\", \"fasdt\"]\n  },\n  ga = {\n    classic: {\n      fab: \"fa-brands\",\n      fad: \"fa-duotone\",\n      fal: \"fa-light\",\n      far: \"fa-regular\",\n      fas: \"fa-solid\",\n      fat: \"fa-thin\"\n    },\n    duotone: {\n      fadr: \"fa-regular\",\n      fadl: \"fa-light\",\n      fadt: \"fa-thin\"\n    },\n    sharp: {\n      fass: \"fa-solid\",\n      fasr: \"fa-regular\",\n      fasl: \"fa-light\",\n      fast: \"fa-thin\"\n    },\n    \"sharp-duotone\": {\n      fasds: \"fa-solid\",\n      fasdr: \"fa-regular\",\n      fasdl: \"fa-light\",\n      fasdt: \"fa-thin\"\n    }\n  },\n  x = [\"fa-solid\", \"fa-regular\", \"fa-light\", \"fa-thin\", \"fa-duotone\", \"fa-brands\"],\n  Ia = [\"fa\", \"fas\", \"far\", \"fal\", \"fat\", \"fad\", \"fadr\", \"fadl\", \"fadt\", \"fab\", \"fass\", \"fasr\", \"fasl\", \"fast\", \"fasds\", \"fasdr\", \"fasdl\", \"fasdt\", ...r$1, ...x],\n  m$1 = [\"solid\", \"regular\", \"light\", \"thin\", \"duotone\", \"brands\"],\n  c$1 = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],\n  F$1 = c$1.concat([11, 12, 13, 14, 15, 16, 17, 18, 19, 20]),\n  ma = [...Object.keys(I$1), ...m$1, \"2xs\", \"xs\", \"sm\", \"lg\", \"xl\", \"2xl\", \"beat\", \"border\", \"fade\", \"beat-fade\", \"bounce\", \"flip-both\", \"flip-horizontal\", \"flip-vertical\", \"flip\", \"fw\", \"inverse\", \"layers-counter\", \"layers-text\", \"layers\", \"li\", \"pull-left\", \"pull-right\", \"pulse\", \"rotate-180\", \"rotate-270\", \"rotate-90\", \"rotate-by\", \"shake\", \"spin-pulse\", \"spin-reverse\", \"spin\", \"stack-1x\", \"stack-2x\", \"stack\", \"ul\", t$1.GROUP, t$1.SWAP_OPACITY, t$1.PRIMARY, t$1.SECONDARY].concat(c$1.map(a => \"\".concat(a, \"x\"))).concat(F$1.map(a => \"w-\".concat(a)));\nvar wa = {\n    \"Font Awesome 5 Free\": {\n      900: \"fas\",\n      400: \"far\"\n    },\n    \"Font Awesome 5 Pro\": {\n      900: \"fas\",\n      400: \"far\",\n      normal: \"far\",\n      300: \"fal\"\n    },\n    \"Font Awesome 5 Brands\": {\n      400: \"fab\",\n      normal: \"fab\"\n    },\n    \"Font Awesome 5 Duotone\": {\n      900: \"fad\"\n    }\n  };\n\nconst NAMESPACE_IDENTIFIER = '___FONT_AWESOME___';\nconst UNITS_IN_GRID = 16;\nconst DEFAULT_CSS_PREFIX = 'fa';\nconst DEFAULT_REPLACEMENT_CLASS = 'svg-inline--fa';\nconst DATA_FA_I2SVG = 'data-fa-i2svg';\nconst DATA_FA_PSEUDO_ELEMENT = 'data-fa-pseudo-element';\nconst DATA_FA_PSEUDO_ELEMENT_PENDING = 'data-fa-pseudo-element-pending';\nconst DATA_PREFIX = 'data-prefix';\nconst DATA_ICON = 'data-icon';\nconst HTML_CLASS_I2SVG_BASE_CLASS = 'fontawesome-i2svg';\nconst MUTATION_APPROACH_ASYNC = 'async';\nconst TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS = ['HTML', 'HEAD', 'STYLE', 'SCRIPT'];\nconst PRODUCTION = (() => {\n  try {\n    return process.env.NODE_ENV === 'production';\n  } catch (e$$1) {\n    return false;\n  }\n})();\nfunction familyProxy(obj) {\n  // Defaults to the classic family if family is not available\n  return new Proxy(obj, {\n    get(target, prop) {\n      return prop in target ? target[prop] : target[s];\n    }\n  });\n}\nconst _PREFIX_TO_STYLE = _objectSpread2({}, S);\n\n// We changed FACSSClassesToStyleId in the icons repo to be canonical and as such, \"classic\" family does not have any\n// duotone styles.  But we do still need duotone in _PREFIX_TO_STYLE below, so we are manually adding\n// {'fa-duotone': 'duotone'}\n_PREFIX_TO_STYLE[s] = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, {\n  'fa-duotone': 'duotone'\n}), S[s]), St['kit']), St['kit-duotone']);\nconst PREFIX_TO_STYLE = familyProxy(_PREFIX_TO_STYLE);\nconst _STYLE_TO_PREFIX = _objectSpread2({}, xt);\n\n// We changed FAStyleIdToShortPrefixId in the icons repo to be canonical and as such, \"classic\" family does not have any\n// duotone styles.  But we do still need duotone in _STYLE_TO_PREFIX below, so we are manually adding {duotone: 'fad'}\n_STYLE_TO_PREFIX[s] = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, {\n  duotone: 'fad'\n}), _STYLE_TO_PREFIX[s]), Et['kit']), Et['kit-duotone']);\nconst STYLE_TO_PREFIX = familyProxy(_STYLE_TO_PREFIX);\nconst _PREFIX_TO_LONG_STYLE = _objectSpread2({}, ga);\n_PREFIX_TO_LONG_STYLE[s] = _objectSpread2(_objectSpread2({}, _PREFIX_TO_LONG_STYLE[s]), Wt['kit']);\nconst PREFIX_TO_LONG_STYLE = familyProxy(_PREFIX_TO_LONG_STYLE);\nconst _LONG_STYLE_TO_PREFIX = _objectSpread2({}, ua);\n_LONG_STYLE_TO_PREFIX[s] = _objectSpread2(_objectSpread2({}, _LONG_STYLE_TO_PREFIX[s]), Ct['kit']);\nconst LONG_STYLE_TO_PREFIX = familyProxy(_LONG_STYLE_TO_PREFIX);\nconst ICON_SELECTION_SYNTAX_PATTERN = p; // eslint-disable-line no-useless-escape\n\nconst LAYERS_TEXT_CLASSNAME = 'fa-layers-text';\nconst FONT_FAMILY_PATTERN = g;\nconst _FONT_WEIGHT_TO_PREFIX = _objectSpread2({}, G);\nconst FONT_WEIGHT_TO_PREFIX = familyProxy(_FONT_WEIGHT_TO_PREFIX);\nconst ATTRIBUTES_WATCHED_FOR_MUTATION = ['class', 'data-prefix', 'data-icon', 'data-fa-transform', 'data-fa-mask'];\nconst DUOTONE_CLASSES = A;\nconst RESERVED_CLASSES = [...At, ...ma];\n\nconst initial = WINDOW.FontAwesomeConfig || {};\nfunction getAttrConfig(attr) {\n  var element = DOCUMENT.querySelector('script[' + attr + ']');\n  if (element) {\n    return element.getAttribute(attr);\n  }\n}\nfunction coerce(val) {\n  // Getting an empty string will occur if the attribute is set on the HTML tag but without a value\n  // We'll assume that this is an indication that it should be toggled to true\n  if (val === '') return true;\n  if (val === 'false') return false;\n  if (val === 'true') return true;\n  return val;\n}\nif (DOCUMENT && typeof DOCUMENT.querySelector === 'function') {\n  const attrs = [['data-family-prefix', 'familyPrefix'], ['data-css-prefix', 'cssPrefix'], ['data-family-default', 'familyDefault'], ['data-style-default', 'styleDefault'], ['data-replacement-class', 'replacementClass'], ['data-auto-replace-svg', 'autoReplaceSvg'], ['data-auto-add-css', 'autoAddCss'], ['data-auto-a11y', 'autoA11y'], ['data-search-pseudo-elements', 'searchPseudoElements'], ['data-observe-mutations', 'observeMutations'], ['data-mutate-approach', 'mutateApproach'], ['data-keep-original-source', 'keepOriginalSource'], ['data-measure-performance', 'measurePerformance'], ['data-show-missing-icons', 'showMissingIcons']];\n  attrs.forEach(_ref => {\n    let [attr, key] = _ref;\n    const val = coerce(getAttrConfig(attr));\n    if (val !== undefined && val !== null) {\n      initial[key] = val;\n    }\n  });\n}\nconst _default = {\n  styleDefault: 'solid',\n  familyDefault: s,\n  cssPrefix: DEFAULT_CSS_PREFIX,\n  replacementClass: DEFAULT_REPLACEMENT_CLASS,\n  autoReplaceSvg: true,\n  autoAddCss: true,\n  autoA11y: true,\n  searchPseudoElements: false,\n  observeMutations: true,\n  mutateApproach: 'async',\n  keepOriginalSource: true,\n  measurePerformance: false,\n  showMissingIcons: true\n};\n\n// familyPrefix is deprecated but we must still support it if present\nif (initial.familyPrefix) {\n  initial.cssPrefix = initial.familyPrefix;\n}\nconst _config = _objectSpread2(_objectSpread2({}, _default), initial);\nif (!_config.autoReplaceSvg) _config.observeMutations = false;\nconst config = {};\nObject.keys(_default).forEach(key => {\n  Object.defineProperty(config, key, {\n    enumerable: true,\n    set: function (val) {\n      _config[key] = val;\n      _onChangeCb.forEach(cb => cb(config));\n    },\n    get: function () {\n      return _config[key];\n    }\n  });\n});\n\n// familyPrefix is deprecated as of 6.2.0 and should be removed in 7.0.0\nObject.defineProperty(config, 'familyPrefix', {\n  enumerable: true,\n  set: function (val) {\n    _config.cssPrefix = val;\n    _onChangeCb.forEach(cb => cb(config));\n  },\n  get: function () {\n    return _config.cssPrefix;\n  }\n});\nWINDOW.FontAwesomeConfig = config;\nconst _onChangeCb = [];\nfunction onChange(cb) {\n  _onChangeCb.push(cb);\n  return () => {\n    _onChangeCb.splice(_onChangeCb.indexOf(cb), 1);\n  };\n}\n\nconst d$2 = UNITS_IN_GRID;\nconst meaninglessTransform = {\n  size: 16,\n  x: 0,\n  y: 0,\n  rotate: 0,\n  flipX: false,\n  flipY: false\n};\nfunction insertCss(css) {\n  if (!css || !IS_DOM) {\n    return;\n  }\n  const style = DOCUMENT.createElement('style');\n  style.setAttribute('type', 'text/css');\n  style.innerHTML = css;\n  const headChildren = DOCUMENT.head.childNodes;\n  let beforeChild = null;\n  for (let i = headChildren.length - 1; i > -1; i--) {\n    const child = headChildren[i];\n    const tagName = (child.tagName || '').toUpperCase();\n    if (['STYLE', 'LINK'].indexOf(tagName) > -1) {\n      beforeChild = child;\n    }\n  }\n  DOCUMENT.head.insertBefore(style, beforeChild);\n  return css;\n}\nconst idPool = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\nfunction nextUniqueId() {\n  let size = 12;\n  let id = '';\n  while (size-- > 0) {\n    id += idPool[Math.random() * 62 | 0];\n  }\n  return id;\n}\nfunction toArray(obj) {\n  const array = [];\n  for (let i = (obj || []).length >>> 0; i--;) {\n    array[i] = obj[i];\n  }\n  return array;\n}\nfunction classArray(node) {\n  if (node.classList) {\n    return toArray(node.classList);\n  } else {\n    return (node.getAttribute('class') || '').split(' ').filter(i => i);\n  }\n}\nfunction htmlEscape(str) {\n  return \"\".concat(str).replace(/&/g, '&amp;').replace(/\"/g, '&quot;').replace(/'/g, '&#39;').replace(/</g, '&lt;').replace(/>/g, '&gt;');\n}\nfunction joinAttributes(attributes) {\n  return Object.keys(attributes || {}).reduce((acc, attributeName) => {\n    return acc + \"\".concat(attributeName, \"=\\\"\").concat(htmlEscape(attributes[attributeName]), \"\\\" \");\n  }, '').trim();\n}\nfunction joinStyles(styles) {\n  return Object.keys(styles || {}).reduce((acc, styleName) => {\n    return acc + \"\".concat(styleName, \": \").concat(styles[styleName].trim(), \";\");\n  }, '');\n}\nfunction transformIsMeaningful(transform) {\n  return transform.size !== meaninglessTransform.size || transform.x !== meaninglessTransform.x || transform.y !== meaninglessTransform.y || transform.rotate !== meaninglessTransform.rotate || transform.flipX || transform.flipY;\n}\nfunction transformForSvg(_ref) {\n  let {\n    transform,\n    containerWidth,\n    iconWidth\n  } = _ref;\n  const outer = {\n    transform: \"translate(\".concat(containerWidth / 2, \" 256)\")\n  };\n  const innerTranslate = \"translate(\".concat(transform.x * 32, \", \").concat(transform.y * 32, \") \");\n  const innerScale = \"scale(\".concat(transform.size / 16 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / 16 * (transform.flipY ? -1 : 1), \") \");\n  const innerRotate = \"rotate(\".concat(transform.rotate, \" 0 0)\");\n  const inner = {\n    transform: \"\".concat(innerTranslate, \" \").concat(innerScale, \" \").concat(innerRotate)\n  };\n  const path = {\n    transform: \"translate(\".concat(iconWidth / 2 * -1, \" -256)\")\n  };\n  return {\n    outer,\n    inner,\n    path\n  };\n}\nfunction transformForCss(_ref2) {\n  let {\n    transform,\n    width = UNITS_IN_GRID,\n    height = UNITS_IN_GRID,\n    startCentered = false\n  } = _ref2;\n  let val = '';\n  if (startCentered && IS_IE) {\n    val += \"translate(\".concat(transform.x / d$2 - width / 2, \"em, \").concat(transform.y / d$2 - height / 2, \"em) \");\n  } else if (startCentered) {\n    val += \"translate(calc(-50% + \".concat(transform.x / d$2, \"em), calc(-50% + \").concat(transform.y / d$2, \"em)) \");\n  } else {\n    val += \"translate(\".concat(transform.x / d$2, \"em, \").concat(transform.y / d$2, \"em) \");\n  }\n  val += \"scale(\".concat(transform.size / d$2 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / d$2 * (transform.flipY ? -1 : 1), \") \");\n  val += \"rotate(\".concat(transform.rotate, \"deg) \");\n  return val;\n}\n\nvar baseStyles = \":root, :host {\\n  --fa-font-solid: normal 900 1em/1 \\\"Font Awesome 6 Free\\\";\\n  --fa-font-regular: normal 400 1em/1 \\\"Font Awesome 6 Free\\\";\\n  --fa-font-light: normal 300 1em/1 \\\"Font Awesome 6 Pro\\\";\\n  --fa-font-thin: normal 100 1em/1 \\\"Font Awesome 6 Pro\\\";\\n  --fa-font-duotone: normal 900 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-duotone-regular: normal 400 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-duotone-light: normal 300 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-duotone-thin: normal 100 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-brands: normal 400 1em/1 \\\"Font Awesome 6 Brands\\\";\\n  --fa-font-sharp-solid: normal 900 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-regular: normal 400 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-light: normal 300 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-thin: normal 100 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-duotone-solid: normal 900 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n  --fa-font-sharp-duotone-regular: normal 400 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n  --fa-font-sharp-duotone-light: normal 300 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n  --fa-font-sharp-duotone-thin: normal 100 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n}\\n\\nsvg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {\\n  overflow: visible;\\n  box-sizing: content-box;\\n}\\n\\n.svg-inline--fa {\\n  display: var(--fa-display, inline-block);\\n  height: 1em;\\n  overflow: visible;\\n  vertical-align: -0.125em;\\n}\\n.svg-inline--fa.fa-2xs {\\n  vertical-align: 0.1em;\\n}\\n.svg-inline--fa.fa-xs {\\n  vertical-align: 0em;\\n}\\n.svg-inline--fa.fa-sm {\\n  vertical-align: -0.0714285705em;\\n}\\n.svg-inline--fa.fa-lg {\\n  vertical-align: -0.2em;\\n}\\n.svg-inline--fa.fa-xl {\\n  vertical-align: -0.25em;\\n}\\n.svg-inline--fa.fa-2xl {\\n  vertical-align: -0.3125em;\\n}\\n.svg-inline--fa.fa-pull-left {\\n  margin-right: var(--fa-pull-margin, 0.3em);\\n  width: auto;\\n}\\n.svg-inline--fa.fa-pull-right {\\n  margin-left: var(--fa-pull-margin, 0.3em);\\n  width: auto;\\n}\\n.svg-inline--fa.fa-li {\\n  width: var(--fa-li-width, 2em);\\n  top: 0.25em;\\n}\\n.svg-inline--fa.fa-fw {\\n  width: var(--fa-fw-width, 1.25em);\\n}\\n\\n.fa-layers svg.svg-inline--fa {\\n  bottom: 0;\\n  left: 0;\\n  margin: auto;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n}\\n\\n.fa-layers-counter, .fa-layers-text {\\n  display: inline-block;\\n  position: absolute;\\n  text-align: center;\\n}\\n\\n.fa-layers {\\n  display: inline-block;\\n  height: 1em;\\n  position: relative;\\n  text-align: center;\\n  vertical-align: -0.125em;\\n  width: 1em;\\n}\\n.fa-layers svg.svg-inline--fa {\\n  transform-origin: center center;\\n}\\n\\n.fa-layers-text {\\n  left: 50%;\\n  top: 50%;\\n  transform: translate(-50%, -50%);\\n  transform-origin: center center;\\n}\\n\\n.fa-layers-counter {\\n  background-color: var(--fa-counter-background-color, #ff253a);\\n  border-radius: var(--fa-counter-border-radius, 1em);\\n  box-sizing: border-box;\\n  color: var(--fa-inverse, #fff);\\n  line-height: var(--fa-counter-line-height, 1);\\n  max-width: var(--fa-counter-max-width, 5em);\\n  min-width: var(--fa-counter-min-width, 1.5em);\\n  overflow: hidden;\\n  padding: var(--fa-counter-padding, 0.25em 0.5em);\\n  right: var(--fa-right, 0);\\n  text-overflow: ellipsis;\\n  top: var(--fa-top, 0);\\n  transform: scale(var(--fa-counter-scale, 0.25));\\n  transform-origin: top right;\\n}\\n\\n.fa-layers-bottom-right {\\n  bottom: var(--fa-bottom, 0);\\n  right: var(--fa-right, 0);\\n  top: auto;\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: bottom right;\\n}\\n\\n.fa-layers-bottom-left {\\n  bottom: var(--fa-bottom, 0);\\n  left: var(--fa-left, 0);\\n  right: auto;\\n  top: auto;\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: bottom left;\\n}\\n\\n.fa-layers-top-right {\\n  top: var(--fa-top, 0);\\n  right: var(--fa-right, 0);\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: top right;\\n}\\n\\n.fa-layers-top-left {\\n  left: var(--fa-left, 0);\\n  right: auto;\\n  top: var(--fa-top, 0);\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: top left;\\n}\\n\\n.fa-1x {\\n  font-size: 1em;\\n}\\n\\n.fa-2x {\\n  font-size: 2em;\\n}\\n\\n.fa-3x {\\n  font-size: 3em;\\n}\\n\\n.fa-4x {\\n  font-size: 4em;\\n}\\n\\n.fa-5x {\\n  font-size: 5em;\\n}\\n\\n.fa-6x {\\n  font-size: 6em;\\n}\\n\\n.fa-7x {\\n  font-size: 7em;\\n}\\n\\n.fa-8x {\\n  font-size: 8em;\\n}\\n\\n.fa-9x {\\n  font-size: 9em;\\n}\\n\\n.fa-10x {\\n  font-size: 10em;\\n}\\n\\n.fa-2xs {\\n  font-size: 0.625em;\\n  line-height: 0.1em;\\n  vertical-align: 0.225em;\\n}\\n\\n.fa-xs {\\n  font-size: 0.75em;\\n  line-height: 0.0833333337em;\\n  vertical-align: 0.125em;\\n}\\n\\n.fa-sm {\\n  font-size: 0.875em;\\n  line-height: 0.0714285718em;\\n  vertical-align: 0.0535714295em;\\n}\\n\\n.fa-lg {\\n  font-size: 1.25em;\\n  line-height: 0.05em;\\n  vertical-align: -0.075em;\\n}\\n\\n.fa-xl {\\n  font-size: 1.5em;\\n  line-height: 0.0416666682em;\\n  vertical-align: -0.125em;\\n}\\n\\n.fa-2xl {\\n  font-size: 2em;\\n  line-height: 0.03125em;\\n  vertical-align: -0.1875em;\\n}\\n\\n.fa-fw {\\n  text-align: center;\\n  width: 1.25em;\\n}\\n\\n.fa-ul {\\n  list-style-type: none;\\n  margin-left: var(--fa-li-margin, 2.5em);\\n  padding-left: 0;\\n}\\n.fa-ul > li {\\n  position: relative;\\n}\\n\\n.fa-li {\\n  left: calc(-1 * var(--fa-li-width, 2em));\\n  position: absolute;\\n  text-align: center;\\n  width: var(--fa-li-width, 2em);\\n  line-height: inherit;\\n}\\n\\n.fa-border {\\n  border-color: var(--fa-border-color, #eee);\\n  border-radius: var(--fa-border-radius, 0.1em);\\n  border-style: var(--fa-border-style, solid);\\n  border-width: var(--fa-border-width, 0.08em);\\n  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);\\n}\\n\\n.fa-pull-left {\\n  float: left;\\n  margin-right: var(--fa-pull-margin, 0.3em);\\n}\\n\\n.fa-pull-right {\\n  float: right;\\n  margin-left: var(--fa-pull-margin, 0.3em);\\n}\\n\\n.fa-beat {\\n  animation-name: fa-beat;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n}\\n\\n.fa-bounce {\\n  animation-name: fa-bounce;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\\n}\\n\\n.fa-fade {\\n  animation-name: fa-fade;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n}\\n\\n.fa-beat-fade {\\n  animation-name: fa-beat-fade;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n}\\n\\n.fa-flip {\\n  animation-name: fa-flip;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n}\\n\\n.fa-shake {\\n  animation-name: fa-shake;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, linear);\\n}\\n\\n.fa-spin {\\n  animation-name: fa-spin;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 2s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, linear);\\n}\\n\\n.fa-spin-reverse {\\n  --fa-animation-direction: reverse;\\n}\\n\\n.fa-pulse,\\n.fa-spin-pulse {\\n  animation-name: fa-spin;\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, steps(8));\\n}\\n\\n@media (prefers-reduced-motion: reduce) {\\n  .fa-beat,\\n.fa-bounce,\\n.fa-fade,\\n.fa-beat-fade,\\n.fa-flip,\\n.fa-pulse,\\n.fa-shake,\\n.fa-spin,\\n.fa-spin-pulse {\\n    animation-delay: -1ms;\\n    animation-duration: 1ms;\\n    animation-iteration-count: 1;\\n    transition-delay: 0s;\\n    transition-duration: 0s;\\n  }\\n}\\n@keyframes fa-beat {\\n  0%, 90% {\\n    transform: scale(1);\\n  }\\n  45% {\\n    transform: scale(var(--fa-beat-scale, 1.25));\\n  }\\n}\\n@keyframes fa-bounce {\\n  0% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n  10% {\\n    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\\n  }\\n  30% {\\n    transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\\n  }\\n  50% {\\n    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\\n  }\\n  57% {\\n    transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\\n  }\\n  64% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n  100% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n}\\n@keyframes fa-fade {\\n  50% {\\n    opacity: var(--fa-fade-opacity, 0.4);\\n  }\\n}\\n@keyframes fa-beat-fade {\\n  0%, 100% {\\n    opacity: var(--fa-beat-fade-opacity, 0.4);\\n    transform: scale(1);\\n  }\\n  50% {\\n    opacity: 1;\\n    transform: scale(var(--fa-beat-fade-scale, 1.125));\\n  }\\n}\\n@keyframes fa-flip {\\n  50% {\\n    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\\n  }\\n}\\n@keyframes fa-shake {\\n  0% {\\n    transform: rotate(-15deg);\\n  }\\n  4% {\\n    transform: rotate(15deg);\\n  }\\n  8%, 24% {\\n    transform: rotate(-18deg);\\n  }\\n  12%, 28% {\\n    transform: rotate(18deg);\\n  }\\n  16% {\\n    transform: rotate(-22deg);\\n  }\\n  20% {\\n    transform: rotate(22deg);\\n  }\\n  32% {\\n    transform: rotate(-12deg);\\n  }\\n  36% {\\n    transform: rotate(12deg);\\n  }\\n  40%, 100% {\\n    transform: rotate(0deg);\\n  }\\n}\\n@keyframes fa-spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.fa-rotate-90 {\\n  transform: rotate(90deg);\\n}\\n\\n.fa-rotate-180 {\\n  transform: rotate(180deg);\\n}\\n\\n.fa-rotate-270 {\\n  transform: rotate(270deg);\\n}\\n\\n.fa-flip-horizontal {\\n  transform: scale(-1, 1);\\n}\\n\\n.fa-flip-vertical {\\n  transform: scale(1, -1);\\n}\\n\\n.fa-flip-both,\\n.fa-flip-horizontal.fa-flip-vertical {\\n  transform: scale(-1, -1);\\n}\\n\\n.fa-rotate-by {\\n  transform: rotate(var(--fa-rotate-angle, 0));\\n}\\n\\n.fa-stack {\\n  display: inline-block;\\n  vertical-align: middle;\\n  height: 2em;\\n  position: relative;\\n  width: 2.5em;\\n}\\n\\n.fa-stack-1x,\\n.fa-stack-2x {\\n  bottom: 0;\\n  left: 0;\\n  margin: auto;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n  z-index: var(--fa-stack-z-index, auto);\\n}\\n\\n.svg-inline--fa.fa-stack-1x {\\n  height: 1em;\\n  width: 1.25em;\\n}\\n.svg-inline--fa.fa-stack-2x {\\n  height: 2em;\\n  width: 2.5em;\\n}\\n\\n.fa-inverse {\\n  color: var(--fa-inverse, #fff);\\n}\\n\\n.sr-only,\\n.fa-sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n\\n.sr-only-focusable:not(:focus),\\n.fa-sr-only-focusable:not(:focus) {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n\\n.svg-inline--fa .fa-primary {\\n  fill: var(--fa-primary-color, currentColor);\\n  opacity: var(--fa-primary-opacity, 1);\\n}\\n\\n.svg-inline--fa .fa-secondary {\\n  fill: var(--fa-secondary-color, currentColor);\\n  opacity: var(--fa-secondary-opacity, 0.4);\\n}\\n\\n.svg-inline--fa.fa-swap-opacity .fa-primary {\\n  opacity: var(--fa-secondary-opacity, 0.4);\\n}\\n\\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\\n  opacity: var(--fa-primary-opacity, 1);\\n}\\n\\n.svg-inline--fa mask .fa-primary,\\n.svg-inline--fa mask .fa-secondary {\\n  fill: black;\\n}\";\n\nfunction css() {\n  const dcp = DEFAULT_CSS_PREFIX;\n  const drc = DEFAULT_REPLACEMENT_CLASS;\n  const fp = config.cssPrefix;\n  const rc = config.replacementClass;\n  let s = baseStyles;\n  if (fp !== dcp || rc !== drc) {\n    const dPatt = new RegExp(\"\\\\.\".concat(dcp, \"\\\\-\"), 'g');\n    const customPropPatt = new RegExp(\"\\\\--\".concat(dcp, \"\\\\-\"), 'g');\n    const rPatt = new RegExp(\"\\\\.\".concat(drc), 'g');\n    s = s.replace(dPatt, \".\".concat(fp, \"-\")).replace(customPropPatt, \"--\".concat(fp, \"-\")).replace(rPatt, \".\".concat(rc));\n  }\n  return s;\n}\nlet _cssInserted = false;\nfunction ensureCss() {\n  if (config.autoAddCss && !_cssInserted) {\n    insertCss(css());\n    _cssInserted = true;\n  }\n}\nvar InjectCSS = {\n  mixout() {\n    return {\n      dom: {\n        css,\n        insertCss: ensureCss\n      }\n    };\n  },\n  hooks() {\n    return {\n      beforeDOMElementCreation() {\n        ensureCss();\n      },\n      beforeI2svg() {\n        ensureCss();\n      }\n    };\n  }\n};\n\nconst w = WINDOW || {};\nif (!w[NAMESPACE_IDENTIFIER]) w[NAMESPACE_IDENTIFIER] = {};\nif (!w[NAMESPACE_IDENTIFIER].styles) w[NAMESPACE_IDENTIFIER].styles = {};\nif (!w[NAMESPACE_IDENTIFIER].hooks) w[NAMESPACE_IDENTIFIER].hooks = {};\nif (!w[NAMESPACE_IDENTIFIER].shims) w[NAMESPACE_IDENTIFIER].shims = [];\nvar namespace = w[NAMESPACE_IDENTIFIER];\n\nconst functions = [];\nconst listener = function () {\n  DOCUMENT.removeEventListener('DOMContentLoaded', listener);\n  loaded = 1;\n  functions.map(fn => fn());\n};\nlet loaded = false;\nif (IS_DOM) {\n  loaded = (DOCUMENT.documentElement.doScroll ? /^loaded|^c/ : /^loaded|^i|^c/).test(DOCUMENT.readyState);\n  if (!loaded) DOCUMENT.addEventListener('DOMContentLoaded', listener);\n}\nfunction domready (fn) {\n  if (!IS_DOM) return;\n  loaded ? setTimeout(fn, 0) : functions.push(fn);\n}\n\nfunction toHtml(abstractNodes) {\n  const {\n    tag,\n    attributes = {},\n    children = []\n  } = abstractNodes;\n  if (typeof abstractNodes === 'string') {\n    return htmlEscape(abstractNodes);\n  } else {\n    return \"<\".concat(tag, \" \").concat(joinAttributes(attributes), \">\").concat(children.map(toHtml).join(''), \"</\").concat(tag, \">\");\n  }\n}\n\nfunction iconFromMapping(mapping, prefix, iconName) {\n  if (mapping && mapping[prefix] && mapping[prefix][iconName]) {\n    return {\n      prefix,\n      iconName,\n      icon: mapping[prefix][iconName]\n    };\n  }\n}\n\n/**\n * Internal helper to bind a function known to have 4 arguments\n * to a given context.\n */\nvar bindInternal4 = function bindInternal4(func, thisContext) {\n  return function (a, b, c, d) {\n    return func.call(thisContext, a, b, c, d);\n  };\n};\n\n/**\n * # Reduce\n *\n * A fast object `.reduce()` implementation.\n *\n * @param  {Object}   subject      The object to reduce over.\n * @param  {Function} fn           The reducer function.\n * @param  {mixed}    initialValue The initial value for the reducer, defaults to subject[0].\n * @param  {Object}   thisContext  The context for the reducer.\n * @return {mixed}                 The final result.\n */\nvar reduce = function fastReduceObject(subject, fn, initialValue, thisContext) {\n  var keys = Object.keys(subject),\n    length = keys.length,\n    iterator = thisContext !== undefined ? bindInternal4(fn, thisContext) : fn,\n    i,\n    key,\n    result;\n  if (initialValue === undefined) {\n    i = 1;\n    result = subject[keys[0]];\n  } else {\n    i = 0;\n    result = initialValue;\n  }\n  for (; i < length; i++) {\n    key = keys[i];\n    result = iterator(result, subject[key], key, subject);\n  }\n  return result;\n};\n\n/**\n * ucs2decode() and codePointAt() are both works of Mathias Bynens and licensed under MIT\n *\n * Copyright Mathias Bynens <https://mathiasbynens.be/>\n\n * Permission is hereby granted, free of charge, to any person obtaining\n * a copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to\n * permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:\n\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software.\n\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\n * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\n * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\n * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\nfunction ucs2decode(string) {\n  const output = [];\n  let counter = 0;\n  const length = string.length;\n  while (counter < length) {\n    const value = string.charCodeAt(counter++);\n    if (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n      const extra = string.charCodeAt(counter++);\n      if ((extra & 0xFC00) == 0xDC00) {\n        // eslint-disable-line eqeqeq\n        output.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n      } else {\n        output.push(value);\n        counter--;\n      }\n    } else {\n      output.push(value);\n    }\n  }\n  return output;\n}\nfunction toHex(unicode) {\n  const decoded = ucs2decode(unicode);\n  return decoded.length === 1 ? decoded[0].toString(16) : null;\n}\nfunction codePointAt(string, index) {\n  const size = string.length;\n  let first = string.charCodeAt(index);\n  let second;\n  if (first >= 0xD800 && first <= 0xDBFF && size > index + 1) {\n    second = string.charCodeAt(index + 1);\n    if (second >= 0xDC00 && second <= 0xDFFF) {\n      return (first - 0xD800) * 0x400 + second - 0xDC00 + 0x10000;\n    }\n  }\n  return first;\n}\n\nfunction normalizeIcons(icons) {\n  return Object.keys(icons).reduce((acc, iconName) => {\n    const icon = icons[iconName];\n    const expanded = !!icon.icon;\n    if (expanded) {\n      acc[icon.iconName] = icon.icon;\n    } else {\n      acc[iconName] = icon;\n    }\n    return acc;\n  }, {});\n}\nfunction defineIcons(prefix, icons) {\n  let params = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  const {\n    skipHooks = false\n  } = params;\n  const normalized = normalizeIcons(icons);\n  if (typeof namespace.hooks.addPack === 'function' && !skipHooks) {\n    namespace.hooks.addPack(prefix, normalizeIcons(icons));\n  } else {\n    namespace.styles[prefix] = _objectSpread2(_objectSpread2({}, namespace.styles[prefix] || {}), normalized);\n  }\n\n  /**\n   * Font Awesome 4 used the prefix of `fa` for all icons. With the introduction\n   * of new styles we needed to differentiate between them. Prefix `fa` is now an alias\n   * for `fas` so we'll ease the upgrade process for our users by automatically defining\n   * this as well.\n   */\n  if (prefix === 'fas') {\n    defineIcons('fa', icons);\n  }\n}\n\nconst duotonePathRe = [/*#__PURE__*/_wrapRegExp(/path d=\"([^\"]+)\".*path d=\"([^\"]+)\"/, {\n  d1: 1,\n  d2: 2\n}), /*#__PURE__*/_wrapRegExp(/path class=\"([^\"]+)\".*d=\"([^\"]+)\".*path class=\"([^\"]+)\".*d=\"([^\"]+)\"/, {\n  cls1: 1,\n  d1: 2,\n  cls2: 3,\n  d2: 4\n}), /*#__PURE__*/_wrapRegExp(/path class=\"([^\"]+)\".*d=\"([^\"]+)\"/, {\n  cls1: 1,\n  d1: 2\n})];\n\nconst {\n  styles,\n  shims\n} = namespace;\nconst FAMILY_NAMES = Object.keys(PREFIX_TO_LONG_STYLE);\nconst PREFIXES_FOR_FAMILY = FAMILY_NAMES.reduce((acc, familyId) => {\n  acc[familyId] = Object.keys(PREFIX_TO_LONG_STYLE[familyId]);\n  return acc;\n}, {});\nlet _defaultUsablePrefix = null;\nlet _byUnicode = {};\nlet _byLigature = {};\nlet _byOldName = {};\nlet _byOldUnicode = {};\nlet _byAlias = {};\nfunction isReserved(name) {\n  return ~RESERVED_CLASSES.indexOf(name);\n}\nfunction getIconName(cssPrefix, cls) {\n  const parts = cls.split('-');\n  const prefix = parts[0];\n  const iconName = parts.slice(1).join('-');\n  if (prefix === cssPrefix && iconName !== '' && !isReserved(iconName)) {\n    return iconName;\n  } else {\n    return null;\n  }\n}\nconst build = () => {\n  const lookup = reducer => {\n    return reduce(styles, (o$$1, style, prefix) => {\n      o$$1[prefix] = reduce(style, reducer, {});\n      return o$$1;\n    }, {});\n  };\n  _byUnicode = lookup((acc, icon, iconName) => {\n    if (icon[3]) {\n      acc[icon[3]] = iconName;\n    }\n    if (icon[2]) {\n      const aliases = icon[2].filter(a$$1 => {\n        return typeof a$$1 === 'number';\n      });\n      aliases.forEach(alias => {\n        acc[alias.toString(16)] = iconName;\n      });\n    }\n    return acc;\n  });\n  _byLigature = lookup((acc, icon, iconName) => {\n    acc[iconName] = iconName;\n    if (icon[2]) {\n      const aliases = icon[2].filter(a$$1 => {\n        return typeof a$$1 === 'string';\n      });\n      aliases.forEach(alias => {\n        acc[alias] = iconName;\n      });\n    }\n    return acc;\n  });\n  _byAlias = lookup((acc, icon, iconName) => {\n    const aliases = icon[2];\n    acc[iconName] = iconName;\n    aliases.forEach(alias => {\n      acc[alias] = iconName;\n    });\n    return acc;\n  });\n\n  // If we have a Kit, we can't determine if regular is available since we\n  // could be auto-fetching it. We'll have to assume that it is available.\n  const hasRegular = 'far' in styles || config.autoFetchSvg;\n  const shimLookups = reduce(shims, (acc, shim) => {\n    const maybeNameMaybeUnicode = shim[0];\n    let prefix = shim[1];\n    const iconName = shim[2];\n    if (prefix === 'far' && !hasRegular) {\n      prefix = 'fas';\n    }\n    if (typeof maybeNameMaybeUnicode === 'string') {\n      acc.names[maybeNameMaybeUnicode] = {\n        prefix,\n        iconName\n      };\n    }\n    if (typeof maybeNameMaybeUnicode === 'number') {\n      acc.unicodes[maybeNameMaybeUnicode.toString(16)] = {\n        prefix,\n        iconName\n      };\n    }\n    return acc;\n  }, {\n    names: {},\n    unicodes: {}\n  });\n  _byOldName = shimLookups.names;\n  _byOldUnicode = shimLookups.unicodes;\n  _defaultUsablePrefix = getCanonicalPrefix(config.styleDefault, {\n    family: config.familyDefault\n  });\n};\nonChange(c$$1 => {\n  _defaultUsablePrefix = getCanonicalPrefix(c$$1.styleDefault, {\n    family: config.familyDefault\n  });\n});\nbuild();\nfunction byUnicode(prefix, unicode) {\n  return (_byUnicode[prefix] || {})[unicode];\n}\nfunction byLigature(prefix, ligature) {\n  return (_byLigature[prefix] || {})[ligature];\n}\nfunction byAlias(prefix, alias) {\n  return (_byAlias[prefix] || {})[alias];\n}\nfunction byOldName(name) {\n  return _byOldName[name] || {\n    prefix: null,\n    iconName: null\n  };\n}\nfunction byOldUnicode(unicode) {\n  const oldUnicode = _byOldUnicode[unicode];\n  const newUnicode = byUnicode('fas', unicode);\n  return oldUnicode || (newUnicode ? {\n    prefix: 'fas',\n    iconName: newUnicode\n  } : null) || {\n    prefix: null,\n    iconName: null\n  };\n}\nfunction getDefaultUsablePrefix() {\n  return _defaultUsablePrefix;\n}\nconst emptyCanonicalIcon = () => {\n  return {\n    prefix: null,\n    iconName: null,\n    rest: []\n  };\n};\nfunction getFamilyId(values) {\n  let family = s;\n  const famProps = FAMILY_NAMES.reduce((acc, familyId) => {\n    acc[familyId] = \"\".concat(config.cssPrefix, \"-\").concat(familyId);\n    return acc;\n  }, {});\n  L.forEach(familyId => {\n    if (values.includes(famProps[familyId]) || values.some(v$$1 => PREFIXES_FOR_FAMILY[familyId].includes(v$$1))) {\n      family = familyId;\n    }\n  });\n  return family;\n}\nfunction getCanonicalPrefix(styleOrPrefix) {\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    family = s\n  } = params;\n  const style = PREFIX_TO_STYLE[family][styleOrPrefix];\n\n  // handles the exception of passing in only a family of 'duotone' with no style\n  if (family === t && !styleOrPrefix) {\n    return 'fad';\n  }\n  const prefix = STYLE_TO_PREFIX[family][styleOrPrefix] || STYLE_TO_PREFIX[family][style];\n  const defined = styleOrPrefix in namespace.styles ? styleOrPrefix : null;\n  const result = prefix || defined || null;\n  return result;\n}\nfunction moveNonFaClassesToRest(classNames) {\n  let rest = [];\n  let iconName = null;\n  classNames.forEach(cls => {\n    const result = getIconName(config.cssPrefix, cls);\n    if (result) {\n      iconName = result;\n    } else if (cls) {\n      rest.push(cls);\n    }\n  });\n  return {\n    iconName,\n    rest\n  };\n}\nfunction sortedUniqueValues(arr) {\n  return arr.sort().filter((value, index, arr) => {\n    return arr.indexOf(value) === index;\n  });\n}\nfunction getCanonicalIcon(values) {\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    skipLookups = false\n  } = params;\n  let givenPrefix = null;\n  const faCombinedClasses = Ia.concat(bt$1);\n  const faStyleOrFamilyClasses = sortedUniqueValues(values.filter(cls => faCombinedClasses.includes(cls)));\n  const nonStyleOrFamilyClasses = sortedUniqueValues(values.filter(cls => !Ia.includes(cls)));\n  const faStyles = faStyleOrFamilyClasses.filter(cls => {\n    givenPrefix = cls;\n    return !P.includes(cls);\n  });\n  const [styleFromValues = null] = faStyles;\n  const family = getFamilyId(faStyleOrFamilyClasses);\n  const canonical = _objectSpread2(_objectSpread2({}, moveNonFaClassesToRest(nonStyleOrFamilyClasses)), {}, {\n    prefix: getCanonicalPrefix(styleFromValues, {\n      family\n    })\n  });\n  return _objectSpread2(_objectSpread2(_objectSpread2({}, canonical), getDefaultCanonicalPrefix({\n    values,\n    family,\n    styles,\n    config,\n    canonical,\n    givenPrefix\n  })), applyShimAndAlias(skipLookups, givenPrefix, canonical));\n}\nfunction applyShimAndAlias(skipLookups, givenPrefix, canonical) {\n  let {\n    prefix,\n    iconName\n  } = canonical;\n  if (skipLookups || !prefix || !iconName) {\n    return {\n      prefix,\n      iconName\n    };\n  }\n  const shim = givenPrefix === 'fa' ? byOldName(iconName) : {};\n  const aliasIconName = byAlias(prefix, iconName);\n  iconName = shim.iconName || aliasIconName || iconName;\n  prefix = shim.prefix || prefix;\n  if (prefix === 'far' && !styles['far'] && styles['fas'] && !config.autoFetchSvg) {\n    // Allow a fallback from the regular style to solid if regular is not available\n    // but only if we aren't auto-fetching SVGs\n    prefix = 'fas';\n  }\n  return {\n    prefix,\n    iconName\n  };\n}\nconst newCanonicalFamilies = L.filter(familyId => {\n  return familyId !== s || familyId !== t;\n});\nconst newCanonicalStyles = Object.keys(ga).filter(key => key !== s).map(key => Object.keys(ga[key])).flat();\nfunction getDefaultCanonicalPrefix(prefixOptions) {\n  const {\n    values,\n    family,\n    canonical,\n    givenPrefix = '',\n    styles = {},\n    config: config$$1 = {}\n  } = prefixOptions;\n  const isDuotoneFamily = family === t;\n  const valuesHasDuotone = values.includes('fa-duotone') || values.includes('fad');\n  const defaultFamilyIsDuotone = config$$1.familyDefault === 'duotone';\n  const canonicalPrefixIsDuotone = canonical.prefix === 'fad' || canonical.prefix === 'fa-duotone';\n  if (!isDuotoneFamily && (valuesHasDuotone || defaultFamilyIsDuotone || canonicalPrefixIsDuotone)) {\n    canonical.prefix = 'fad';\n  }\n  if (values.includes('fa-brands') || values.includes('fab')) {\n    canonical.prefix = 'fab';\n  }\n  if (!canonical.prefix && newCanonicalFamilies.includes(family)) {\n    const validPrefix = Object.keys(styles).find(key => newCanonicalStyles.includes(key));\n    if (validPrefix || config$$1.autoFetchSvg) {\n      const defaultPrefix = pt.get(family).defaultShortPrefixId;\n      canonical.prefix = defaultPrefix;\n      canonical.iconName = byAlias(canonical.prefix, canonical.iconName) || canonical.iconName;\n    }\n  }\n  if (canonical.prefix === 'fa' || givenPrefix === 'fa') {\n    // The fa prefix is not canonical. So if it has made it through until this point\n    // we will shift it to the correct prefix.\n    canonical.prefix = getDefaultUsablePrefix() || 'fas';\n  }\n  return canonical;\n}\n\nclass Library {\n  constructor() {\n    this.definitions = {};\n  }\n  add() {\n    for (var _len = arguments.length, definitions = new Array(_len), _key = 0; _key < _len; _key++) {\n      definitions[_key] = arguments[_key];\n    }\n    const additions = definitions.reduce(this._pullDefinitions, {});\n    Object.keys(additions).forEach(key => {\n      this.definitions[key] = _objectSpread2(_objectSpread2({}, this.definitions[key] || {}), additions[key]);\n      defineIcons(key, additions[key]);\n\n      // TODO can we stop doing this? We can't get the icons by 'fa-solid' any longer so this probably needs to change\n      const longPrefix = PREFIX_TO_LONG_STYLE[s][key];\n      if (longPrefix) defineIcons(longPrefix, additions[key]);\n      build();\n    });\n  }\n  reset() {\n    this.definitions = {};\n  }\n  _pullDefinitions(additions, definition) {\n    const normalized = definition.prefix && definition.iconName && definition.icon ? {\n      0: definition\n    } : definition;\n    Object.keys(normalized).map(key => {\n      const {\n        prefix,\n        iconName,\n        icon\n      } = normalized[key];\n      const aliases = icon[2];\n      if (!additions[prefix]) additions[prefix] = {};\n      if (aliases.length > 0) {\n        aliases.forEach(alias => {\n          if (typeof alias === 'string') {\n            additions[prefix][alias] = icon;\n          }\n        });\n      }\n      additions[prefix][iconName] = icon;\n    });\n    return additions;\n  }\n}\n\nlet _plugins = [];\nlet _hooks = {};\nconst providers = {};\nconst defaultProviderKeys = Object.keys(providers);\nfunction registerPlugins(nextPlugins, _ref) {\n  let {\n    mixoutsTo: obj\n  } = _ref;\n  _plugins = nextPlugins;\n  _hooks = {};\n  Object.keys(providers).forEach(k => {\n    if (defaultProviderKeys.indexOf(k) === -1) {\n      delete providers[k];\n    }\n  });\n  _plugins.forEach(plugin => {\n    const mixout = plugin.mixout ? plugin.mixout() : {};\n    Object.keys(mixout).forEach(tk => {\n      if (typeof mixout[tk] === 'function') {\n        obj[tk] = mixout[tk];\n      }\n      if (typeof mixout[tk] === 'object') {\n        Object.keys(mixout[tk]).forEach(sk => {\n          if (!obj[tk]) {\n            obj[tk] = {};\n          }\n          obj[tk][sk] = mixout[tk][sk];\n        });\n      }\n    });\n    if (plugin.hooks) {\n      const hooks = plugin.hooks();\n      Object.keys(hooks).forEach(hook => {\n        if (!_hooks[hook]) {\n          _hooks[hook] = [];\n        }\n        _hooks[hook].push(hooks[hook]);\n      });\n    }\n    if (plugin.provides) {\n      plugin.provides(providers);\n    }\n  });\n  return obj;\n}\nfunction chainHooks(hook, accumulator) {\n  for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    args[_key - 2] = arguments[_key];\n  }\n  const hookFns = _hooks[hook] || [];\n  hookFns.forEach(hookFn => {\n    accumulator = hookFn.apply(null, [accumulator, ...args]); // eslint-disable-line no-useless-call\n  });\n  return accumulator;\n}\nfunction callHooks(hook) {\n  for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    args[_key2 - 1] = arguments[_key2];\n  }\n  const hookFns = _hooks[hook] || [];\n  hookFns.forEach(hookFn => {\n    hookFn.apply(null, args);\n  });\n  return undefined;\n}\nfunction callProvided() {\n  const hook = arguments[0];\n  const args = Array.prototype.slice.call(arguments, 1);\n  return providers[hook] ? providers[hook].apply(null, args) : undefined;\n}\n\nfunction findIconDefinition(iconLookup) {\n  if (iconLookup.prefix === 'fa') {\n    iconLookup.prefix = 'fas';\n  }\n  let {\n    iconName\n  } = iconLookup;\n  const prefix = iconLookup.prefix || getDefaultUsablePrefix();\n  if (!iconName) return;\n  iconName = byAlias(prefix, iconName) || iconName;\n  return iconFromMapping(library.definitions, prefix, iconName) || iconFromMapping(namespace.styles, prefix, iconName);\n}\nconst library = new Library();\nconst noAuto = () => {\n  config.autoReplaceSvg = false;\n  config.observeMutations = false;\n  callHooks('noAuto');\n};\nconst dom = {\n  i2svg: function () {\n    let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (IS_DOM) {\n      callHooks('beforeI2svg', params);\n      callProvided('pseudoElements2svg', params);\n      return callProvided('i2svg', params);\n    } else {\n      return Promise.reject(new Error('Operation requires a DOM of some kind.'));\n    }\n  },\n  watch: function () {\n    let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const {\n      autoReplaceSvgRoot\n    } = params;\n    if (config.autoReplaceSvg === false) {\n      config.autoReplaceSvg = true;\n    }\n    config.observeMutations = true;\n    domready(() => {\n      autoReplace({\n        autoReplaceSvgRoot\n      });\n      callHooks('watch', params);\n    });\n  }\n};\nconst parse = {\n  icon: icon => {\n    if (icon === null) {\n      return null;\n    }\n    if (typeof icon === 'object' && icon.prefix && icon.iconName) {\n      return {\n        prefix: icon.prefix,\n        iconName: byAlias(icon.prefix, icon.iconName) || icon.iconName\n      };\n    }\n    if (Array.isArray(icon) && icon.length === 2) {\n      const iconName = icon[1].indexOf('fa-') === 0 ? icon[1].slice(3) : icon[1];\n      const prefix = getCanonicalPrefix(icon[0]);\n      return {\n        prefix,\n        iconName: byAlias(prefix, iconName) || iconName\n      };\n    }\n    if (typeof icon === 'string' && (icon.indexOf(\"\".concat(config.cssPrefix, \"-\")) > -1 || icon.match(ICON_SELECTION_SYNTAX_PATTERN))) {\n      const canonicalIcon = getCanonicalIcon(icon.split(' '), {\n        skipLookups: true\n      });\n      return {\n        prefix: canonicalIcon.prefix || getDefaultUsablePrefix(),\n        iconName: byAlias(canonicalIcon.prefix, canonicalIcon.iconName) || canonicalIcon.iconName\n      };\n    }\n    if (typeof icon === 'string') {\n      const prefix = getDefaultUsablePrefix();\n      return {\n        prefix,\n        iconName: byAlias(prefix, icon) || icon\n      };\n    }\n  }\n};\nconst api = {\n  noAuto,\n  config,\n  dom,\n  parse,\n  library,\n  findIconDefinition,\n  toHtml\n};\nconst autoReplace = function () {\n  let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n    autoReplaceSvgRoot = DOCUMENT\n  } = params;\n  if ((Object.keys(namespace.styles).length > 0 || config.autoFetchSvg) && IS_DOM && config.autoReplaceSvg) api.dom.i2svg({\n    node: autoReplaceSvgRoot\n  });\n};\n\nfunction domVariants(val, abstractCreator) {\n  Object.defineProperty(val, 'abstract', {\n    get: abstractCreator\n  });\n  Object.defineProperty(val, 'html', {\n    get: function () {\n      return val.abstract.map(a => toHtml(a));\n    }\n  });\n  Object.defineProperty(val, 'node', {\n    get: function () {\n      if (!IS_DOM) return;\n      const container = DOCUMENT.createElement('div');\n      container.innerHTML = val.html;\n      return container.children;\n    }\n  });\n  return val;\n}\n\nfunction asIcon (_ref) {\n  let {\n    children,\n    main,\n    mask,\n    attributes,\n    styles,\n    transform\n  } = _ref;\n  if (transformIsMeaningful(transform) && main.found && !mask.found) {\n    const {\n      width,\n      height\n    } = main;\n    const offset = {\n      x: width / height / 2,\n      y: 0.5\n    };\n    attributes['style'] = joinStyles(_objectSpread2(_objectSpread2({}, styles), {}, {\n      'transform-origin': \"\".concat(offset.x + transform.x / 16, \"em \").concat(offset.y + transform.y / 16, \"em\")\n    }));\n  }\n  return [{\n    tag: 'svg',\n    attributes,\n    children\n  }];\n}\n\nfunction asSymbol (_ref) {\n  let {\n    prefix,\n    iconName,\n    children,\n    attributes,\n    symbol\n  } = _ref;\n  const id = symbol === true ? \"\".concat(prefix, \"-\").concat(config.cssPrefix, \"-\").concat(iconName) : symbol;\n  return [{\n    tag: 'svg',\n    attributes: {\n      style: 'display: none;'\n    },\n    children: [{\n      tag: 'symbol',\n      attributes: _objectSpread2(_objectSpread2({}, attributes), {}, {\n        id\n      }),\n      children\n    }]\n  }];\n}\n\nfunction makeInlineSvgAbstract(params) {\n  const {\n    icons: {\n      main,\n      mask\n    },\n    prefix,\n    iconName,\n    transform,\n    symbol,\n    title,\n    maskId,\n    titleId,\n    extra,\n    watchable = false\n  } = params;\n  const {\n    width,\n    height\n  } = mask.found ? mask : main;\n  const isUploadedIcon = Lt.includes(prefix);\n  const attrClass = [config.replacementClass, iconName ? \"\".concat(config.cssPrefix, \"-\").concat(iconName) : ''].filter(c$$1 => extra.classes.indexOf(c$$1) === -1).filter(c$$1 => c$$1 !== '' || !!c$$1).concat(extra.classes).join(' ');\n  let content = {\n    children: [],\n    attributes: _objectSpread2(_objectSpread2({}, extra.attributes), {}, {\n      'data-prefix': prefix,\n      'data-icon': iconName,\n      'class': attrClass,\n      'role': extra.attributes.role || 'img',\n      'xmlns': 'http://www.w3.org/2000/svg',\n      'viewBox': \"0 0 \".concat(width, \" \").concat(height)\n    })\n  };\n  const uploadedIconWidthStyle = isUploadedIcon && !~extra.classes.indexOf('fa-fw') ? {\n    width: \"\".concat(width / height * 16 * 0.0625, \"em\")\n  } : {};\n  if (watchable) {\n    content.attributes[DATA_FA_I2SVG] = '';\n  }\n  if (title) {\n    content.children.push({\n      tag: 'title',\n      attributes: {\n        id: content.attributes['aria-labelledby'] || \"title-\".concat(titleId || nextUniqueId())\n      },\n      children: [title]\n    });\n    delete content.attributes.title;\n  }\n  const args = _objectSpread2(_objectSpread2({}, content), {}, {\n    prefix,\n    iconName,\n    main,\n    mask,\n    maskId,\n    transform,\n    symbol,\n    styles: _objectSpread2(_objectSpread2({}, uploadedIconWidthStyle), extra.styles)\n  });\n  const {\n    children,\n    attributes\n  } = mask.found && main.found ? callProvided('generateAbstractMask', args) || {\n    children: [],\n    attributes: {}\n  } : callProvided('generateAbstractIcon', args) || {\n    children: [],\n    attributes: {}\n  };\n  args.children = children;\n  args.attributes = attributes;\n  if (symbol) {\n    return asSymbol(args);\n  } else {\n    return asIcon(args);\n  }\n}\nfunction makeLayersTextAbstract(params) {\n  const {\n    content,\n    width,\n    height,\n    transform,\n    title,\n    extra,\n    watchable = false\n  } = params;\n  const attributes = _objectSpread2(_objectSpread2(_objectSpread2({}, extra.attributes), title ? {\n    'title': title\n  } : {}), {}, {\n    'class': extra.classes.join(' ')\n  });\n  if (watchable) {\n    attributes[DATA_FA_I2SVG] = '';\n  }\n  const styles = _objectSpread2({}, extra.styles);\n  if (transformIsMeaningful(transform)) {\n    styles['transform'] = transformForCss({\n      transform,\n      startCentered: true,\n      width,\n      height\n    });\n    styles['-webkit-transform'] = styles['transform'];\n  }\n  const styleString = joinStyles(styles);\n  if (styleString.length > 0) {\n    attributes['style'] = styleString;\n  }\n  const val = [];\n  val.push({\n    tag: 'span',\n    attributes,\n    children: [content]\n  });\n  if (title) {\n    val.push({\n      tag: 'span',\n      attributes: {\n        class: 'sr-only'\n      },\n      children: [title]\n    });\n  }\n  return val;\n}\nfunction makeLayersCounterAbstract(params) {\n  const {\n    content,\n    title,\n    extra\n  } = params;\n  const attributes = _objectSpread2(_objectSpread2(_objectSpread2({}, extra.attributes), title ? {\n    'title': title\n  } : {}), {}, {\n    'class': extra.classes.join(' ')\n  });\n  const styleString = joinStyles(extra.styles);\n  if (styleString.length > 0) {\n    attributes['style'] = styleString;\n  }\n  const val = [];\n  val.push({\n    tag: 'span',\n    attributes,\n    children: [content]\n  });\n  if (title) {\n    val.push({\n      tag: 'span',\n      attributes: {\n        class: 'sr-only'\n      },\n      children: [title]\n    });\n  }\n  return val;\n}\n\nconst {\n  styles: styles$1\n} = namespace;\nfunction asFoundIcon(icon) {\n  const width = icon[0];\n  const height = icon[1];\n  const [vectorData] = icon.slice(4);\n  let element = null;\n  if (Array.isArray(vectorData)) {\n    element = {\n      tag: 'g',\n      attributes: {\n        class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.GROUP)\n      },\n      children: [{\n        tag: 'path',\n        attributes: {\n          class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.SECONDARY),\n          fill: 'currentColor',\n          d: vectorData[0]\n        }\n      }, {\n        tag: 'path',\n        attributes: {\n          class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.PRIMARY),\n          fill: 'currentColor',\n          d: vectorData[1]\n        }\n      }]\n    };\n  } else {\n    element = {\n      tag: 'path',\n      attributes: {\n        fill: 'currentColor',\n        d: vectorData\n      }\n    };\n  }\n  return {\n    found: true,\n    width,\n    height,\n    icon: element\n  };\n}\nconst missingIconResolutionMixin = {\n  found: false,\n  width: 512,\n  height: 512\n};\nfunction maybeNotifyMissing(iconName, prefix) {\n  if (!PRODUCTION && !config.showMissingIcons && iconName) {\n    console.error(\"Icon with name \\\"\".concat(iconName, \"\\\" and prefix \\\"\").concat(prefix, \"\\\" is missing.\"));\n  }\n}\nfunction findIcon(iconName, prefix) {\n  let givenPrefix = prefix;\n  if (prefix === 'fa' && config.styleDefault !== null) {\n    prefix = getDefaultUsablePrefix();\n  }\n  return new Promise((resolve, reject) => {\n    if (givenPrefix === 'fa') {\n      const shim = byOldName(iconName) || {};\n      iconName = shim.iconName || iconName;\n      prefix = shim.prefix || prefix;\n    }\n    if (iconName && prefix && styles$1[prefix] && styles$1[prefix][iconName]) {\n      const icon = styles$1[prefix][iconName];\n      return resolve(asFoundIcon(icon));\n    }\n    maybeNotifyMissing(iconName, prefix);\n    resolve(_objectSpread2(_objectSpread2({}, missingIconResolutionMixin), {}, {\n      icon: config.showMissingIcons && iconName ? callProvided('missingIconAbstract') || {} : {}\n    }));\n  });\n}\n\nconst noop$1 = () => {};\nconst p$2 = config.measurePerformance && PERFORMANCE && PERFORMANCE.mark && PERFORMANCE.measure ? PERFORMANCE : {\n  mark: noop$1,\n  measure: noop$1\n};\nconst preamble = \"FA \\\"6.7.2\\\"\";\nconst begin = name => {\n  p$2.mark(\"\".concat(preamble, \" \").concat(name, \" begins\"));\n  return () => end(name);\n};\nconst end = name => {\n  p$2.mark(\"\".concat(preamble, \" \").concat(name, \" ends\"));\n  p$2.measure(\"\".concat(preamble, \" \").concat(name), \"\".concat(preamble, \" \").concat(name, \" begins\"), \"\".concat(preamble, \" \").concat(name, \" ends\"));\n};\nvar perf = {\n  begin,\n  end\n};\n\nconst noop$2 = () => {};\nfunction isWatched(node) {\n  const i2svg = node.getAttribute ? node.getAttribute(DATA_FA_I2SVG) : null;\n  return typeof i2svg === 'string';\n}\nfunction hasPrefixAndIcon(node) {\n  const prefix = node.getAttribute ? node.getAttribute(DATA_PREFIX) : null;\n  const icon = node.getAttribute ? node.getAttribute(DATA_ICON) : null;\n  return prefix && icon;\n}\nfunction hasBeenReplaced(node) {\n  return node && node.classList && node.classList.contains && node.classList.contains(config.replacementClass);\n}\nfunction getMutator() {\n  if (config.autoReplaceSvg === true) {\n    return mutators.replace;\n  }\n  const mutator = mutators[config.autoReplaceSvg];\n  return mutator || mutators.replace;\n}\nfunction createElementNS(tag) {\n  return DOCUMENT.createElementNS('http://www.w3.org/2000/svg', tag);\n}\nfunction createElement(tag) {\n  return DOCUMENT.createElement(tag);\n}\nfunction convertSVG(abstractObj) {\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    ceFn = abstractObj.tag === 'svg' ? createElementNS : createElement\n  } = params;\n  if (typeof abstractObj === 'string') {\n    return DOCUMENT.createTextNode(abstractObj);\n  }\n  const tag = ceFn(abstractObj.tag);\n  Object.keys(abstractObj.attributes || []).forEach(function (key) {\n    tag.setAttribute(key, abstractObj.attributes[key]);\n  });\n  const children = abstractObj.children || [];\n  children.forEach(function (child) {\n    tag.appendChild(convertSVG(child, {\n      ceFn\n    }));\n  });\n  return tag;\n}\nfunction nodeAsComment(node) {\n  let comment = \" \".concat(node.outerHTML, \" \");\n  /* BEGIN.ATTRIBUTION */\n  comment = \"\".concat(comment, \"Font Awesome fontawesome.com \");\n  /* END.ATTRIBUTION */\n  return comment;\n}\nconst mutators = {\n  replace: function (mutation) {\n    const node = mutation[0];\n    if (node.parentNode) {\n      mutation[1].forEach(abstract => {\n        node.parentNode.insertBefore(convertSVG(abstract), node);\n      });\n      if (node.getAttribute(DATA_FA_I2SVG) === null && config.keepOriginalSource) {\n        let comment = DOCUMENT.createComment(nodeAsComment(node));\n        node.parentNode.replaceChild(comment, node);\n      } else {\n        node.remove();\n      }\n    }\n  },\n  nest: function (mutation) {\n    const node = mutation[0];\n    const abstract = mutation[1];\n\n    // If we already have a replaced node we do not want to continue nesting within it.\n    // Short-circuit to the standard replacement\n    if (~classArray(node).indexOf(config.replacementClass)) {\n      return mutators.replace(mutation);\n    }\n    const forSvg = new RegExp(\"\".concat(config.cssPrefix, \"-.*\"));\n    delete abstract[0].attributes.id;\n    if (abstract[0].attributes.class) {\n      const splitClasses = abstract[0].attributes.class.split(' ').reduce((acc, cls) => {\n        if (cls === config.replacementClass || cls.match(forSvg)) {\n          acc.toSvg.push(cls);\n        } else {\n          acc.toNode.push(cls);\n        }\n        return acc;\n      }, {\n        toNode: [],\n        toSvg: []\n      });\n      abstract[0].attributes.class = splitClasses.toSvg.join(' ');\n      if (splitClasses.toNode.length === 0) {\n        node.removeAttribute('class');\n      } else {\n        node.setAttribute('class', splitClasses.toNode.join(' '));\n      }\n    }\n    const newInnerHTML = abstract.map(a => toHtml(a)).join('\\n');\n    node.setAttribute(DATA_FA_I2SVG, '');\n    node.innerHTML = newInnerHTML;\n  }\n};\nfunction performOperationSync(op) {\n  op();\n}\nfunction perform(mutations, callback) {\n  const callbackFunction = typeof callback === 'function' ? callback : noop$2;\n  if (mutations.length === 0) {\n    callbackFunction();\n  } else {\n    let frame = performOperationSync;\n    if (config.mutateApproach === MUTATION_APPROACH_ASYNC) {\n      frame = WINDOW.requestAnimationFrame || performOperationSync;\n    }\n    frame(() => {\n      const mutator = getMutator();\n      const mark = perf.begin('mutate');\n      mutations.map(mutator);\n      mark();\n      callbackFunction();\n    });\n  }\n}\nlet disabled = false;\nfunction disableObservation() {\n  disabled = true;\n}\nfunction enableObservation() {\n  disabled = false;\n}\nlet mo = null;\nfunction observe(options) {\n  if (!MUTATION_OBSERVER) {\n    return;\n  }\n  if (!config.observeMutations) {\n    return;\n  }\n  const {\n    treeCallback = noop$2,\n    nodeCallback = noop$2,\n    pseudoElementsCallback = noop$2,\n    observeMutationsRoot = DOCUMENT\n  } = options;\n  mo = new MUTATION_OBSERVER(objects => {\n    if (disabled) return;\n    const defaultPrefix = getDefaultUsablePrefix();\n    toArray(objects).forEach(mutationRecord => {\n      if (mutationRecord.type === 'childList' && mutationRecord.addedNodes.length > 0 && !isWatched(mutationRecord.addedNodes[0])) {\n        if (config.searchPseudoElements) {\n          pseudoElementsCallback(mutationRecord.target);\n        }\n        treeCallback(mutationRecord.target);\n      }\n      if (mutationRecord.type === 'attributes' && mutationRecord.target.parentNode && config.searchPseudoElements) {\n        pseudoElementsCallback(mutationRecord.target.parentNode);\n      }\n      if (mutationRecord.type === 'attributes' && isWatched(mutationRecord.target) && ~ATTRIBUTES_WATCHED_FOR_MUTATION.indexOf(mutationRecord.attributeName)) {\n        if (mutationRecord.attributeName === 'class' && hasPrefixAndIcon(mutationRecord.target)) {\n          const {\n            prefix,\n            iconName\n          } = getCanonicalIcon(classArray(mutationRecord.target));\n          mutationRecord.target.setAttribute(DATA_PREFIX, prefix || defaultPrefix);\n          if (iconName) mutationRecord.target.setAttribute(DATA_ICON, iconName);\n        } else if (hasBeenReplaced(mutationRecord.target)) {\n          nodeCallback(mutationRecord.target);\n        }\n      }\n    });\n  });\n  if (!IS_DOM) return;\n  mo.observe(observeMutationsRoot, {\n    childList: true,\n    attributes: true,\n    characterData: true,\n    subtree: true\n  });\n}\nfunction disconnect() {\n  if (!mo) return;\n  mo.disconnect();\n}\n\nfunction styleParser (node) {\n  const style = node.getAttribute('style');\n  let val = [];\n  if (style) {\n    val = style.split(';').reduce((acc, style) => {\n      const styles = style.split(':');\n      const prop = styles[0];\n      const value = styles.slice(1);\n      if (prop && value.length > 0) {\n        acc[prop] = value.join(':').trim();\n      }\n      return acc;\n    }, {});\n  }\n  return val;\n}\n\nfunction classParser (node) {\n  const existingPrefix = node.getAttribute('data-prefix');\n  const existingIconName = node.getAttribute('data-icon');\n  const innerText = node.innerText !== undefined ? node.innerText.trim() : '';\n  let val = getCanonicalIcon(classArray(node));\n  if (!val.prefix) {\n    val.prefix = getDefaultUsablePrefix();\n  }\n  if (existingPrefix && existingIconName) {\n    val.prefix = existingPrefix;\n    val.iconName = existingIconName;\n  }\n  if (val.iconName && val.prefix) {\n    return val;\n  }\n  if (val.prefix && innerText.length > 0) {\n    val.iconName = byLigature(val.prefix, node.innerText) || byUnicode(val.prefix, toHex(node.innerText));\n  }\n  if (!val.iconName && config.autoFetchSvg && node.firstChild && node.firstChild.nodeType === Node.TEXT_NODE) {\n    val.iconName = node.firstChild.data;\n  }\n  return val;\n}\n\nfunction attributesParser (node) {\n  const extraAttributes = toArray(node.attributes).reduce((acc, attr) => {\n    if (acc.name !== 'class' && acc.name !== 'style') {\n      acc[attr.name] = attr.value;\n    }\n    return acc;\n  }, {});\n  const title = node.getAttribute('title');\n  const titleId = node.getAttribute('data-fa-title-id');\n  if (config.autoA11y) {\n    if (title) {\n      extraAttributes['aria-labelledby'] = \"\".concat(config.replacementClass, \"-title-\").concat(titleId || nextUniqueId());\n    } else {\n      extraAttributes['aria-hidden'] = 'true';\n      extraAttributes['focusable'] = 'false';\n    }\n  }\n  return extraAttributes;\n}\n\nfunction blankMeta() {\n  return {\n    iconName: null,\n    title: null,\n    titleId: null,\n    prefix: null,\n    transform: meaninglessTransform,\n    symbol: false,\n    mask: {\n      iconName: null,\n      prefix: null,\n      rest: []\n    },\n    maskId: null,\n    extra: {\n      classes: [],\n      styles: {},\n      attributes: {}\n    }\n  };\n}\nfunction parseMeta(node) {\n  let parser = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    styleParser: true\n  };\n  const {\n    iconName,\n    prefix,\n    rest: extraClasses\n  } = classParser(node);\n  const extraAttributes = attributesParser(node);\n  const pluginMeta = chainHooks('parseNodeAttributes', {}, node);\n  let extraStyles = parser.styleParser ? styleParser(node) : [];\n  return _objectSpread2({\n    iconName,\n    title: node.getAttribute('title'),\n    titleId: node.getAttribute('data-fa-title-id'),\n    prefix,\n    transform: meaninglessTransform,\n    mask: {\n      iconName: null,\n      prefix: null,\n      rest: []\n    },\n    maskId: null,\n    symbol: false,\n    extra: {\n      classes: extraClasses,\n      styles: extraStyles,\n      attributes: extraAttributes\n    }\n  }, pluginMeta);\n}\n\nconst {\n  styles: styles$2\n} = namespace;\nfunction generateMutation(node) {\n  const nodeMeta = config.autoReplaceSvg === 'nest' ? parseMeta(node, {\n    styleParser: false\n  }) : parseMeta(node);\n  if (~nodeMeta.extra.classes.indexOf(LAYERS_TEXT_CLASSNAME)) {\n    return callProvided('generateLayersText', node, nodeMeta);\n  } else {\n    return callProvided('generateSvgReplacementMutation', node, nodeMeta);\n  }\n}\nfunction getKnownPrefixes() {\n  return [...Ft, ...Ia];\n}\nfunction onTree(root) {\n  let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  if (!IS_DOM) return Promise.resolve();\n  const htmlClassList = DOCUMENT.documentElement.classList;\n  const hclAdd = suffix => htmlClassList.add(\"\".concat(HTML_CLASS_I2SVG_BASE_CLASS, \"-\").concat(suffix));\n  const hclRemove = suffix => htmlClassList.remove(\"\".concat(HTML_CLASS_I2SVG_BASE_CLASS, \"-\").concat(suffix));\n  const prefixes = config.autoFetchSvg ? getKnownPrefixes() : P.concat(Object.keys(styles$2));\n  if (!prefixes.includes('fa')) {\n    prefixes.push('fa');\n  }\n  const prefixesDomQuery = [\".\".concat(LAYERS_TEXT_CLASSNAME, \":not([\").concat(DATA_FA_I2SVG, \"])\")].concat(prefixes.map(p$$1 => \".\".concat(p$$1, \":not([\").concat(DATA_FA_I2SVG, \"])\"))).join(', ');\n  if (prefixesDomQuery.length === 0) {\n    return Promise.resolve();\n  }\n  let candidates = [];\n  try {\n    candidates = toArray(root.querySelectorAll(prefixesDomQuery));\n  } catch (e$$1) {\n    // noop\n  }\n  if (candidates.length > 0) {\n    hclAdd('pending');\n    hclRemove('complete');\n  } else {\n    return Promise.resolve();\n  }\n  const mark = perf.begin('onTree');\n  const mutations = candidates.reduce((acc, node) => {\n    try {\n      const mutation = generateMutation(node);\n      if (mutation) {\n        acc.push(mutation);\n      }\n    } catch (e$$1) {\n      if (!PRODUCTION) {\n        if (e$$1.name === 'MissingIcon') {\n          console.error(e$$1);\n        }\n      }\n    }\n    return acc;\n  }, []);\n  return new Promise((resolve, reject) => {\n    Promise.all(mutations).then(resolvedMutations => {\n      perform(resolvedMutations, () => {\n        hclAdd('active');\n        hclAdd('complete');\n        hclRemove('pending');\n        if (typeof callback === 'function') callback();\n        mark();\n        resolve();\n      });\n    }).catch(e$$1 => {\n      mark();\n      reject(e$$1);\n    });\n  });\n}\nfunction onNode(node) {\n  let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  generateMutation(node).then(mutation => {\n    if (mutation) {\n      perform([mutation], callback);\n    }\n  });\n}\nfunction resolveIcons(next) {\n  return function (maybeIconDefinition) {\n    let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const iconDefinition = (maybeIconDefinition || {}).icon ? maybeIconDefinition : findIconDefinition(maybeIconDefinition || {});\n    let {\n      mask\n    } = params;\n    if (mask) {\n      mask = (mask || {}).icon ? mask : findIconDefinition(mask || {});\n    }\n    return next(iconDefinition, _objectSpread2(_objectSpread2({}, params), {}, {\n      mask\n    }));\n  };\n}\nconst render = function (iconDefinition) {\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    transform = meaninglessTransform,\n    symbol = false,\n    mask = null,\n    maskId = null,\n    title = null,\n    titleId = null,\n    classes = [],\n    attributes = {},\n    styles = {}\n  } = params;\n  if (!iconDefinition) return;\n  const {\n    prefix,\n    iconName,\n    icon\n  } = iconDefinition;\n  return domVariants(_objectSpread2({\n    type: 'icon'\n  }, iconDefinition), () => {\n    callHooks('beforeDOMElementCreation', {\n      iconDefinition,\n      params\n    });\n    if (config.autoA11y) {\n      if (title) {\n        attributes['aria-labelledby'] = \"\".concat(config.replacementClass, \"-title-\").concat(titleId || nextUniqueId());\n      } else {\n        attributes['aria-hidden'] = 'true';\n        attributes['focusable'] = 'false';\n      }\n    }\n    return makeInlineSvgAbstract({\n      icons: {\n        main: asFoundIcon(icon),\n        mask: mask ? asFoundIcon(mask.icon) : {\n          found: false,\n          width: null,\n          height: null,\n          icon: {}\n        }\n      },\n      prefix,\n      iconName,\n      transform: _objectSpread2(_objectSpread2({}, meaninglessTransform), transform),\n      symbol,\n      title,\n      maskId,\n      titleId,\n      extra: {\n        attributes,\n        styles,\n        classes\n      }\n    });\n  });\n};\nvar ReplaceElements = {\n  mixout() {\n    return {\n      icon: resolveIcons(render)\n    };\n  },\n  hooks() {\n    return {\n      mutationObserverCallbacks(accumulator) {\n        accumulator.treeCallback = onTree;\n        accumulator.nodeCallback = onNode;\n        return accumulator;\n      }\n    };\n  },\n  provides(providers$$1) {\n    providers$$1.i2svg = function (params) {\n      const {\n        node = DOCUMENT,\n        callback = () => {}\n      } = params;\n      return onTree(node, callback);\n    };\n    providers$$1.generateSvgReplacementMutation = function (node, nodeMeta) {\n      const {\n        iconName,\n        title,\n        titleId,\n        prefix,\n        transform,\n        symbol,\n        mask,\n        maskId,\n        extra\n      } = nodeMeta;\n      return new Promise((resolve, reject) => {\n        Promise.all([findIcon(iconName, prefix), mask.iconName ? findIcon(mask.iconName, mask.prefix) : Promise.resolve({\n          found: false,\n          width: 512,\n          height: 512,\n          icon: {}\n        })]).then(_ref => {\n          let [main, mask] = _ref;\n          resolve([node, makeInlineSvgAbstract({\n            icons: {\n              main,\n              mask\n            },\n            prefix,\n            iconName,\n            transform,\n            symbol,\n            maskId,\n            title,\n            titleId,\n            extra,\n            watchable: true\n          })]);\n        }).catch(reject);\n      });\n    };\n    providers$$1.generateAbstractIcon = function (_ref2) {\n      let {\n        children,\n        attributes,\n        main,\n        transform,\n        styles\n      } = _ref2;\n      const styleString = joinStyles(styles);\n      if (styleString.length > 0) {\n        attributes['style'] = styleString;\n      }\n      let nextChild;\n      if (transformIsMeaningful(transform)) {\n        nextChild = callProvided('generateAbstractTransformGrouping', {\n          main,\n          transform,\n          containerWidth: main.width,\n          iconWidth: main.width\n        });\n      }\n      children.push(nextChild || main.icon);\n      return {\n        children,\n        attributes\n      };\n    };\n  }\n};\n\nvar Layers = {\n  mixout() {\n    return {\n      layer(assembler) {\n        let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        const {\n          classes = []\n        } = params;\n        return domVariants({\n          type: 'layer'\n        }, () => {\n          callHooks('beforeDOMElementCreation', {\n            assembler,\n            params\n          });\n          let children = [];\n          assembler(args => {\n            Array.isArray(args) ? args.map(a => {\n              children = children.concat(a.abstract);\n            }) : children = children.concat(args.abstract);\n          });\n          return [{\n            tag: 'span',\n            attributes: {\n              class: [\"\".concat(config.cssPrefix, \"-layers\"), ...classes].join(' ')\n            },\n            children\n          }];\n        });\n      }\n    };\n  }\n};\n\nvar LayersCounter = {\n  mixout() {\n    return {\n      counter(content) {\n        let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        const {\n          title = null,\n          classes = [],\n          attributes = {},\n          styles = {}\n        } = params;\n        return domVariants({\n          type: 'counter',\n          content\n        }, () => {\n          callHooks('beforeDOMElementCreation', {\n            content,\n            params\n          });\n          return makeLayersCounterAbstract({\n            content: content.toString(),\n            title,\n            extra: {\n              attributes,\n              styles,\n              classes: [\"\".concat(config.cssPrefix, \"-layers-counter\"), ...classes]\n            }\n          });\n        });\n      }\n    };\n  }\n};\n\nvar LayersText = {\n  mixout() {\n    return {\n      text(content) {\n        let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        const {\n          transform = meaninglessTransform,\n          title = null,\n          classes = [],\n          attributes = {},\n          styles = {}\n        } = params;\n        return domVariants({\n          type: 'text',\n          content\n        }, () => {\n          callHooks('beforeDOMElementCreation', {\n            content,\n            params\n          });\n          return makeLayersTextAbstract({\n            content,\n            transform: _objectSpread2(_objectSpread2({}, meaninglessTransform), transform),\n            title,\n            extra: {\n              attributes,\n              styles,\n              classes: [\"\".concat(config.cssPrefix, \"-layers-text\"), ...classes]\n            }\n          });\n        });\n      }\n    };\n  },\n  provides(providers$$1) {\n    providers$$1.generateLayersText = function (node, nodeMeta) {\n      const {\n        title,\n        transform,\n        extra\n      } = nodeMeta;\n      let width = null;\n      let height = null;\n      if (IS_IE) {\n        const computedFontSize = parseInt(getComputedStyle(node).fontSize, 10);\n        const boundingClientRect = node.getBoundingClientRect();\n        width = boundingClientRect.width / computedFontSize;\n        height = boundingClientRect.height / computedFontSize;\n      }\n      if (config.autoA11y && !title) {\n        extra.attributes['aria-hidden'] = 'true';\n      }\n      return Promise.resolve([node, makeLayersTextAbstract({\n        content: node.innerHTML,\n        width,\n        height,\n        transform,\n        title,\n        extra,\n        watchable: true\n      })]);\n    };\n  }\n};\n\nconst CLEAN_CONTENT_PATTERN = new RegExp('\\u{22}', 'ug');\nconst SECONDARY_UNICODE_RANGE = [1105920, 1112319];\nconst _FONT_FAMILY_WEIGHT_TO_PREFIX = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, {\n  FontAwesome: {\n    normal: 'fas',\n    400: 'fas'\n  }\n}), lt), wa), Yt);\nconst FONT_FAMILY_WEIGHT_TO_PREFIX = Object.keys(_FONT_FAMILY_WEIGHT_TO_PREFIX).reduce((acc, key) => {\n  acc[key.toLowerCase()] = _FONT_FAMILY_WEIGHT_TO_PREFIX[key];\n  return acc;\n}, {});\nconst FONT_FAMILY_WEIGHT_FALLBACK = Object.keys(FONT_FAMILY_WEIGHT_TO_PREFIX).reduce((acc, fontFamily) => {\n  const weights = FONT_FAMILY_WEIGHT_TO_PREFIX[fontFamily];\n  acc[fontFamily] = weights[900] || [...Object.entries(weights)][0][1];\n  return acc;\n}, {});\nfunction hexValueFromContent(content) {\n  const cleaned = content.replace(CLEAN_CONTENT_PATTERN, '');\n  const codePoint = codePointAt(cleaned, 0);\n  const isPrependTen = codePoint >= SECONDARY_UNICODE_RANGE[0] && codePoint <= SECONDARY_UNICODE_RANGE[1];\n  const isDoubled = cleaned.length === 2 ? cleaned[0] === cleaned[1] : false;\n  return {\n    value: isDoubled ? toHex(cleaned[0]) : toHex(cleaned),\n    isSecondary: isPrependTen || isDoubled\n  };\n}\nfunction getPrefix(fontFamily, fontWeight) {\n  const fontFamilySanitized = fontFamily.replace(/^['\"]|['\"]$/g, '').toLowerCase();\n  const fontWeightInteger = parseInt(fontWeight);\n  const fontWeightSanitized = isNaN(fontWeightInteger) ? 'normal' : fontWeightInteger;\n  return (FONT_FAMILY_WEIGHT_TO_PREFIX[fontFamilySanitized] || {})[fontWeightSanitized] || FONT_FAMILY_WEIGHT_FALLBACK[fontFamilySanitized];\n}\nfunction replaceForPosition(node, position) {\n  const pendingAttribute = \"\".concat(DATA_FA_PSEUDO_ELEMENT_PENDING).concat(position.replace(':', '-'));\n  return new Promise((resolve, reject) => {\n    if (node.getAttribute(pendingAttribute) !== null) {\n      // This node is already being processed\n      return resolve();\n    }\n    const children = toArray(node.children);\n    const alreadyProcessedPseudoElement = children.filter(c$$1 => c$$1.getAttribute(DATA_FA_PSEUDO_ELEMENT) === position)[0];\n    const styles = WINDOW.getComputedStyle(node, position);\n    const fontFamily = styles.getPropertyValue('font-family');\n    const fontFamilyMatch = fontFamily.match(FONT_FAMILY_PATTERN);\n    const fontWeight = styles.getPropertyValue('font-weight');\n    const content = styles.getPropertyValue('content');\n    if (alreadyProcessedPseudoElement && !fontFamilyMatch) {\n      // If we've already processed it but the current computed style does not result in a font-family,\n      // that probably means that a class name that was previously present to make the icon has been\n      // removed. So we now should delete the icon.\n      node.removeChild(alreadyProcessedPseudoElement);\n      return resolve();\n    } else if (fontFamilyMatch && content !== 'none' && content !== '') {\n      const content = styles.getPropertyValue('content');\n      let prefix = getPrefix(fontFamily, fontWeight);\n      const {\n        value: hexValue,\n        isSecondary\n      } = hexValueFromContent(content);\n      const isV4 = fontFamilyMatch[0].startsWith('FontAwesome');\n      let iconName = byUnicode(prefix, hexValue);\n      let iconIdentifier = iconName;\n      if (isV4) {\n        const iconName4 = byOldUnicode(hexValue);\n        if (iconName4.iconName && iconName4.prefix) {\n          iconName = iconName4.iconName;\n          prefix = iconName4.prefix;\n        }\n      }\n\n      // Only convert the pseudo element in this ::before/::after position into an icon if we haven't\n      // already done so with the same prefix and iconName\n      if (iconName && !isSecondary && (!alreadyProcessedPseudoElement || alreadyProcessedPseudoElement.getAttribute(DATA_PREFIX) !== prefix || alreadyProcessedPseudoElement.getAttribute(DATA_ICON) !== iconIdentifier)) {\n        node.setAttribute(pendingAttribute, iconIdentifier);\n        if (alreadyProcessedPseudoElement) {\n          // Delete the old one, since we're replacing it with a new one\n          node.removeChild(alreadyProcessedPseudoElement);\n        }\n        const meta = blankMeta();\n        const {\n          extra\n        } = meta;\n        extra.attributes[DATA_FA_PSEUDO_ELEMENT] = position;\n        findIcon(iconName, prefix).then(main => {\n          const abstract = makeInlineSvgAbstract(_objectSpread2(_objectSpread2({}, meta), {}, {\n            icons: {\n              main,\n              mask: emptyCanonicalIcon()\n            },\n            prefix,\n            iconName: iconIdentifier,\n            extra,\n            watchable: true\n          }));\n          const element = DOCUMENT.createElementNS('http://www.w3.org/2000/svg', 'svg');\n          if (position === '::before') {\n            node.insertBefore(element, node.firstChild);\n          } else {\n            node.appendChild(element);\n          }\n          element.outerHTML = abstract.map(a$$1 => toHtml(a$$1)).join('\\n');\n          node.removeAttribute(pendingAttribute);\n          resolve();\n        }).catch(reject);\n      } else {\n        resolve();\n      }\n    } else {\n      resolve();\n    }\n  });\n}\nfunction replace(node) {\n  return Promise.all([replaceForPosition(node, '::before'), replaceForPosition(node, '::after')]);\n}\nfunction processable(node) {\n  return node.parentNode !== document.head && !~TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS.indexOf(node.tagName.toUpperCase()) && !node.getAttribute(DATA_FA_PSEUDO_ELEMENT) && (!node.parentNode || node.parentNode.tagName !== 'svg');\n}\nfunction searchPseudoElements(root) {\n  if (!IS_DOM) return;\n  return new Promise((resolve, reject) => {\n    const operations = toArray(root.querySelectorAll('*')).filter(processable).map(replace);\n    const end = perf.begin('searchPseudoElements');\n    disableObservation();\n    Promise.all(operations).then(() => {\n      end();\n      enableObservation();\n      resolve();\n    }).catch(() => {\n      end();\n      enableObservation();\n      reject();\n    });\n  });\n}\nvar PseudoElements = {\n  hooks() {\n    return {\n      mutationObserverCallbacks(accumulator) {\n        accumulator.pseudoElementsCallback = searchPseudoElements;\n        return accumulator;\n      }\n    };\n  },\n  provides(providers) {\n    providers.pseudoElements2svg = function (params) {\n      const {\n        node = DOCUMENT\n      } = params;\n      if (config.searchPseudoElements) {\n        searchPseudoElements(node);\n      }\n    };\n  }\n};\n\nlet _unwatched = false;\nvar MutationObserver$1 = {\n  mixout() {\n    return {\n      dom: {\n        unwatch() {\n          disableObservation();\n          _unwatched = true;\n        }\n      }\n    };\n  },\n  hooks() {\n    return {\n      bootstrap() {\n        observe(chainHooks('mutationObserverCallbacks', {}));\n      },\n      noAuto() {\n        disconnect();\n      },\n      watch(params) {\n        const {\n          observeMutationsRoot\n        } = params;\n        if (_unwatched) {\n          enableObservation();\n        } else {\n          observe(chainHooks('mutationObserverCallbacks', {\n            observeMutationsRoot\n          }));\n        }\n      }\n    };\n  }\n};\n\nconst parseTransformString = transformString => {\n  let transform = {\n    size: 16,\n    x: 0,\n    y: 0,\n    flipX: false,\n    flipY: false,\n    rotate: 0\n  };\n  return transformString.toLowerCase().split(' ').reduce((acc, n) => {\n    const parts = n.toLowerCase().split('-');\n    const first = parts[0];\n    let rest = parts.slice(1).join('-');\n    if (first && rest === 'h') {\n      acc.flipX = true;\n      return acc;\n    }\n    if (first && rest === 'v') {\n      acc.flipY = true;\n      return acc;\n    }\n    rest = parseFloat(rest);\n    if (isNaN(rest)) {\n      return acc;\n    }\n    switch (first) {\n      case 'grow':\n        acc.size = acc.size + rest;\n        break;\n      case 'shrink':\n        acc.size = acc.size - rest;\n        break;\n      case 'left':\n        acc.x = acc.x - rest;\n        break;\n      case 'right':\n        acc.x = acc.x + rest;\n        break;\n      case 'up':\n        acc.y = acc.y - rest;\n        break;\n      case 'down':\n        acc.y = acc.y + rest;\n        break;\n      case 'rotate':\n        acc.rotate = acc.rotate + rest;\n        break;\n    }\n    return acc;\n  }, transform);\n};\nvar PowerTransforms = {\n  mixout() {\n    return {\n      parse: {\n        transform: transformString => {\n          return parseTransformString(transformString);\n        }\n      }\n    };\n  },\n  hooks() {\n    return {\n      parseNodeAttributes(accumulator, node) {\n        const transformString = node.getAttribute('data-fa-transform');\n        if (transformString) {\n          accumulator.transform = parseTransformString(transformString);\n        }\n        return accumulator;\n      }\n    };\n  },\n  provides(providers) {\n    providers.generateAbstractTransformGrouping = function (_ref) {\n      let {\n        main,\n        transform,\n        containerWidth,\n        iconWidth\n      } = _ref;\n      const outer = {\n        transform: \"translate(\".concat(containerWidth / 2, \" 256)\")\n      };\n      const innerTranslate = \"translate(\".concat(transform.x * 32, \", \").concat(transform.y * 32, \") \");\n      const innerScale = \"scale(\".concat(transform.size / 16 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / 16 * (transform.flipY ? -1 : 1), \") \");\n      const innerRotate = \"rotate(\".concat(transform.rotate, \" 0 0)\");\n      const inner = {\n        transform: \"\".concat(innerTranslate, \" \").concat(innerScale, \" \").concat(innerRotate)\n      };\n      const path = {\n        transform: \"translate(\".concat(iconWidth / 2 * -1, \" -256)\")\n      };\n      const operations = {\n        outer,\n        inner,\n        path\n      };\n      return {\n        tag: 'g',\n        attributes: _objectSpread2({}, operations.outer),\n        children: [{\n          tag: 'g',\n          attributes: _objectSpread2({}, operations.inner),\n          children: [{\n            tag: main.icon.tag,\n            children: main.icon.children,\n            attributes: _objectSpread2(_objectSpread2({}, main.icon.attributes), operations.path)\n          }]\n        }]\n      };\n    };\n  }\n};\n\nconst ALL_SPACE = {\n  x: 0,\n  y: 0,\n  width: '100%',\n  height: '100%'\n};\nfunction fillBlack(abstract) {\n  let force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  if (abstract.attributes && (abstract.attributes.fill || force)) {\n    abstract.attributes.fill = 'black';\n  }\n  return abstract;\n}\nfunction deGroup(abstract) {\n  if (abstract.tag === 'g') {\n    return abstract.children;\n  } else {\n    return [abstract];\n  }\n}\nvar Masks = {\n  hooks() {\n    return {\n      parseNodeAttributes(accumulator, node) {\n        const maskData = node.getAttribute('data-fa-mask');\n        const mask = !maskData ? emptyCanonicalIcon() : getCanonicalIcon(maskData.split(' ').map(i => i.trim()));\n        if (!mask.prefix) {\n          mask.prefix = getDefaultUsablePrefix();\n        }\n        accumulator.mask = mask;\n        accumulator.maskId = node.getAttribute('data-fa-mask-id');\n        return accumulator;\n      }\n    };\n  },\n  provides(providers) {\n    providers.generateAbstractMask = function (_ref) {\n      let {\n        children,\n        attributes,\n        main,\n        mask,\n        maskId: explicitMaskId,\n        transform\n      } = _ref;\n      const {\n        width: mainWidth,\n        icon: mainPath\n      } = main;\n      const {\n        width: maskWidth,\n        icon: maskPath\n      } = mask;\n      const trans = transformForSvg({\n        transform,\n        containerWidth: maskWidth,\n        iconWidth: mainWidth\n      });\n      const maskRect = {\n        tag: 'rect',\n        attributes: _objectSpread2(_objectSpread2({}, ALL_SPACE), {}, {\n          fill: 'white'\n        })\n      };\n      const maskInnerGroupChildrenMixin = mainPath.children ? {\n        children: mainPath.children.map(fillBlack)\n      } : {};\n      const maskInnerGroup = {\n        tag: 'g',\n        attributes: _objectSpread2({}, trans.inner),\n        children: [fillBlack(_objectSpread2({\n          tag: mainPath.tag,\n          attributes: _objectSpread2(_objectSpread2({}, mainPath.attributes), trans.path)\n        }, maskInnerGroupChildrenMixin))]\n      };\n      const maskOuterGroup = {\n        tag: 'g',\n        attributes: _objectSpread2({}, trans.outer),\n        children: [maskInnerGroup]\n      };\n      const maskId = \"mask-\".concat(explicitMaskId || nextUniqueId());\n      const clipId = \"clip-\".concat(explicitMaskId || nextUniqueId());\n      const maskTag = {\n        tag: 'mask',\n        attributes: _objectSpread2(_objectSpread2({}, ALL_SPACE), {}, {\n          id: maskId,\n          maskUnits: 'userSpaceOnUse',\n          maskContentUnits: 'userSpaceOnUse'\n        }),\n        children: [maskRect, maskOuterGroup]\n      };\n      const defs = {\n        tag: 'defs',\n        children: [{\n          tag: 'clipPath',\n          attributes: {\n            id: clipId\n          },\n          children: deGroup(maskPath)\n        }, maskTag]\n      };\n      children.push(defs, {\n        tag: 'rect',\n        attributes: _objectSpread2({\n          fill: 'currentColor',\n          'clip-path': \"url(#\".concat(clipId, \")\"),\n          mask: \"url(#\".concat(maskId, \")\")\n        }, ALL_SPACE)\n      });\n      return {\n        children,\n        attributes\n      };\n    };\n  }\n};\n\nvar MissingIconIndicator = {\n  provides(providers) {\n    let reduceMotion = false;\n    if (WINDOW.matchMedia) {\n      reduceMotion = WINDOW.matchMedia('(prefers-reduced-motion: reduce)').matches;\n    }\n    providers.missingIconAbstract = function () {\n      const gChildren = [];\n      const FILL = {\n        fill: 'currentColor'\n      };\n      const ANIMATION_BASE = {\n        attributeType: 'XML',\n        repeatCount: 'indefinite',\n        dur: '2s'\n      };\n\n      // Ring\n      gChildren.push({\n        tag: 'path',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          d: 'M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z'\n        })\n      });\n      const OPACITY_ANIMATE = _objectSpread2(_objectSpread2({}, ANIMATION_BASE), {}, {\n        attributeName: 'opacity'\n      });\n      const dot = {\n        tag: 'circle',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          cx: '256',\n          cy: '364',\n          r: '28'\n        }),\n        children: []\n      };\n      if (!reduceMotion) {\n        dot.children.push({\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, ANIMATION_BASE), {}, {\n            attributeName: 'r',\n            values: '28;14;28;28;14;28;'\n          })\n        }, {\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n            values: '1;0;1;1;0;1;'\n          })\n        });\n      }\n      gChildren.push(dot);\n      gChildren.push({\n        tag: 'path',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          opacity: '1',\n          d: 'M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z'\n        }),\n        children: reduceMotion ? [] : [{\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n            values: '1;0;0;0;0;1;'\n          })\n        }]\n      });\n      if (!reduceMotion) {\n        // Exclamation\n        gChildren.push({\n          tag: 'path',\n          attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n            opacity: '0',\n            d: 'M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z'\n          }),\n          children: [{\n            tag: 'animate',\n            attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n              values: '0;0;1;1;0;0;'\n            })\n          }]\n        });\n      }\n      return {\n        tag: 'g',\n        attributes: {\n          'class': 'missing'\n        },\n        children: gChildren\n      };\n    };\n  }\n};\n\nvar SvgSymbols = {\n  hooks() {\n    return {\n      parseNodeAttributes(accumulator, node) {\n        const symbolData = node.getAttribute('data-fa-symbol');\n        const symbol = symbolData === null ? false : symbolData === '' ? true : symbolData;\n        accumulator['symbol'] = symbol;\n        return accumulator;\n      }\n    };\n  }\n};\n\nvar plugins = [InjectCSS, ReplaceElements, Layers, LayersCounter, LayersText, PseudoElements, MutationObserver$1, PowerTransforms, Masks, MissingIconIndicator, SvgSymbols];\n\nregisterPlugins(plugins, {\n  mixoutsTo: api\n});\nconst noAuto$1 = api.noAuto;\nconst config$1 = api.config;\nconst library$1 = api.library;\nconst dom$1 = api.dom;\nconst parse$1 = api.parse;\nconst findIconDefinition$1 = api.findIconDefinition;\nconst toHtml$1 = api.toHtml;\nconst icon = api.icon;\nconst layer = api.layer;\nconst text = api.text;\nconst counter = api.counter;\n\nexport { noAuto$1 as noAuto, config$1 as config, library$1 as library, dom$1 as dom, parse$1 as parse, findIconDefinition$1 as findIconDefinition, toHtml$1 as toHtml, icon, layer, text, counter, api };\n"], "mappings": ";AAKA,SAAS,gBAAgB,GAAGA,IAAGC,IAAG;AAChC,UAAQD,KAAI,eAAeA,EAAC,MAAM,IAAI,OAAO,eAAe,GAAGA,IAAG;AAAA,IAChE,OAAOC;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,UAAU;AAAA,EACZ,CAAC,IAAI,EAAED,EAAC,IAAIC,IAAG;AACjB;AACA,SAAS,UAAUA,IAAG,GAAG;AACvB,MAAI,cAAc,OAAO,KAAK,SAAS,EAAG,OAAM,IAAI,UAAU,oDAAoD;AAClH,EAAAA,GAAE,YAAY,OAAO,OAAO,KAAK,EAAE,WAAW;AAAA,IAC5C,aAAa;AAAA,MACX,OAAOA;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC,GAAG,OAAO,eAAeA,IAAG,aAAa;AAAA,IACxC,UAAU;AAAA,EACZ,CAAC,GAAG,KAAK,gBAAgBA,IAAG,CAAC;AAC/B;AACA,SAAS,QAAQ,GAAGD,IAAG;AACrB,MAAIC,KAAI,OAAO,KAAK,CAAC;AACrB,MAAI,OAAO,uBAAuB;AAChC,QAAIC,KAAI,OAAO,sBAAsB,CAAC;AACtC,IAAAF,OAAME,KAAIA,GAAE,OAAO,SAAUF,IAAG;AAC9B,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAC/C,CAAC,IAAIC,GAAE,KAAK,MAAMA,IAAGC,EAAC;AAAA,EACxB;AACA,SAAOD;AACT;AACA,SAAS,eAAe,GAAG;AACzB,WAASD,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AACzC,QAAIC,KAAI,QAAQ,UAAUD,EAAC,IAAI,UAAUA,EAAC,IAAI,CAAC;AAC/C,IAAAA,KAAI,IAAI,QAAQ,OAAOC,EAAC,GAAG,IAAE,EAAE,QAAQ,SAAUD,IAAG;AAClD,sBAAgB,GAAGA,IAAGC,GAAED,EAAC,CAAC;AAAA,IAC5B,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0BC,EAAC,CAAC,IAAI,QAAQ,OAAOA,EAAC,CAAC,EAAE,QAAQ,SAAUD,IAAG;AAChJ,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyBC,IAAGD,EAAC,CAAC;AAAA,IACnE,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,gBAAgBC,IAAG,GAAG;AAC7B,SAAO,kBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAAUA,IAAGE,IAAG;AAC9F,WAAOF,GAAE,YAAYE,IAAGF;AAAA,EAC1B,GAAG,gBAAgBA,IAAG,CAAC;AACzB;AACA,SAAS,aAAaA,IAAGD,IAAG;AAC1B,MAAI,YAAY,OAAOC,MAAK,CAACA,GAAG,QAAOA;AACvC,MAAI,IAAIA,GAAE,OAAO,WAAW;AAC5B,MAAI,WAAW,GAAG;AAChB,QAAI,IAAI,EAAE,KAAKA,IAAGD,MAAK,SAAS;AAChC,QAAI,YAAY,OAAO,EAAG,QAAO;AACjC,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACpE;AACA,UAAQ,aAAaA,KAAI,SAAS,QAAQC,EAAC;AAC7C;AACA,SAAS,eAAeA,IAAG;AACzB,MAAI,IAAI,aAAaA,IAAG,QAAQ;AAChC,SAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AACxC;AACA,SAAS,cAAc;AACrB,gBAAc,SAAUE,IAAGH,IAAG;AAC5B,WAAO,IAAI,YAAYG,IAAG,QAAQH,EAAC;AAAA,EACrC;AACA,MAAI,IAAI,OAAO,WACbA,KAAI,oBAAI,QAAQ;AAClB,WAAS,YAAYG,IAAGF,IAAGG,IAAG;AAC5B,QAAIF,KAAI,OAAOC,IAAGF,EAAC;AACnB,WAAOD,GAAE,IAAIE,IAAGE,MAAKJ,GAAE,IAAIG,EAAC,CAAC,GAAG,gBAAgBD,IAAG,YAAY,SAAS;AAAA,EAC1E;AACA,WAAS,YAAYC,IAAGF,IAAG;AACzB,QAAIG,KAAIJ,GAAE,IAAIC,EAAC;AACf,WAAO,OAAO,KAAKG,EAAC,EAAE,OAAO,SAAUJ,IAAGC,IAAG;AAC3C,UAAIC,KAAIE,GAAEH,EAAC;AACX,UAAI,YAAY,OAAOC,GAAG,CAAAF,GAAEC,EAAC,IAAIE,GAAED,EAAC;AAAA,WAAO;AACzC,iBAAS,IAAI,GAAG,WAAWC,GAAED,GAAE,CAAC,CAAC,KAAK,IAAI,IAAIA,GAAE,SAAS;AACzD,QAAAF,GAAEC,EAAC,IAAIE,GAAED,GAAE,CAAC,CAAC;AAAA,MACf;AACA,aAAOF;AAAA,IACT,GAAG,uBAAO,OAAO,IAAI,CAAC;AAAA,EACxB;AACA,SAAO,UAAU,aAAa,MAAM,GAAG,YAAY,UAAU,OAAO,SAAUA,IAAG;AAC/E,QAAIC,KAAI,EAAE,KAAK,KAAK,MAAMD,EAAC;AAC3B,QAAIC,IAAG;AACL,MAAAA,GAAE,SAAS,YAAYA,IAAG,IAAI;AAC9B,UAAIG,KAAIH,GAAE;AACV,MAAAG,OAAMA,GAAE,SAAS,YAAYA,IAAG,IAAI;AAAA,IACtC;AACA,WAAOH;AAAA,EACT,GAAG,YAAY,UAAU,OAAO,OAAO,IAAI,SAAUA,IAAGG,IAAG;AACzD,QAAI,YAAY,OAAOA,IAAG;AACxB,UAAIF,KAAIF,GAAE,IAAI,IAAI;AAClB,aAAO,EAAE,OAAO,OAAO,EAAE,KAAK,MAAMC,IAAGG,GAAE,QAAQ,gBAAgB,SAAUD,IAAGH,IAAG;AAC/E,YAAIC,KAAIC,GAAEF,EAAC;AACX,eAAO,OAAO,MAAM,QAAQC,EAAC,IAAIA,GAAE,KAAK,GAAG,IAAIA;AAAA,MACjD,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,cAAc,OAAOG,IAAG;AAC1B,UAAI,IAAI;AACR,aAAO,EAAE,OAAO,OAAO,EAAE,KAAK,MAAMH,IAAG,WAAY;AACjD,YAAIE,KAAI;AACR,eAAO,YAAY,OAAOA,GAAEA,GAAE,SAAS,CAAC,MAAMA,KAAI,CAAC,EAAE,MAAM,KAAKA,EAAC,GAAG,KAAK,YAAYA,IAAG,CAAC,CAAC,GAAGC,GAAE,MAAM,MAAMD,EAAC;AAAA,MAC9G,CAAC;AAAA,IACH;AACA,WAAO,EAAE,OAAO,OAAO,EAAE,KAAK,MAAMF,IAAGG,EAAC;AAAA,EAC1C,GAAG,YAAY,MAAM,MAAM,SAAS;AACtC;AAEA,IAAM,OAAO,MAAM;AAAC;AACpB,IAAI,UAAU,CAAC;AACf,IAAI,YAAY,CAAC;AACjB,IAAI,qBAAqB;AACzB,IAAI,eAAe;AAAA,EACjB,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAI;AACF,MAAI,OAAO,WAAW,YAAa,WAAU;AAC7C,MAAI,OAAO,aAAa,YAAa,aAAY;AACjD,MAAI,OAAO,qBAAqB,YAAa,sBAAqB;AAClE,MAAI,OAAO,gBAAgB,YAAa,gBAAe;AACzD,SAAS,GAAG;AAAC;AACb,IAAM;AAAA,EACJ,YAAY;AACd,IAAI,QAAQ,aAAa,CAAC;AAC1B,IAAM,SAAS;AACf,IAAM,WAAW;AACjB,IAAM,oBAAoB;AAC1B,IAAM,cAAc;AACpB,IAAM,aAAa,CAAC,CAAC,OAAO;AAC5B,IAAM,SAAS,CAAC,CAAC,SAAS,mBAAmB,CAAC,CAAC,SAAS,QAAQ,OAAO,SAAS,qBAAqB,cAAc,OAAO,SAAS,kBAAkB;AACrJ,IAAM,QAAQ,CAAC,UAAU,QAAQ,MAAM,KAAK,CAAC,UAAU,QAAQ,UAAU;AAEzE,IAAI,IAAI;AAAR,IACE,IAAI;AACN,IAAI,IAAI;AAAA,EACJ,SAAS;AAAA,IACP,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,YAAY;AAAA,IACZ,KAAK;AAAA,IACL,cAAc;AAAA,IACd,KAAK;AAAA,IACL,YAAY;AAAA,IACZ,KAAK;AAAA,IACL,WAAW;AAAA,IACX,KAAK;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,MAAM;AAAA,IACN,cAAc;AAAA,IACd,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,cAAc;AAAA,IACd,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,WAAW;AAAA,EACb;AAAA,EACA,iBAAiB;AAAA,IACf,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,cAAc;AAAA,IACd,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,WAAW;AAAA,EACb;AACF;AAhDF,IAiDE,IAAI;AAAA,EACF,OAAO;AAAA,EACP,cAAc;AAAA,EACd,SAAS;AAAA,EACT,WAAW;AACb;AAtDF,IAuDE,IAAI,CAAC,cAAc,cAAc,YAAY,kBAAkB;AACjE,IAAI,IAAI;AAAR,IACE,IAAI;AADN,IAEE,IAAI;AAFN,IAGE,IAAI;AAHN,IAIE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACjB,IAAI,IAAI;AAAA,EACJ,SAAS;AAAA,IACP,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,SAAS;AAAA,IACP,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,OAAO;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,iBAAiB;AAAA,IACf,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AACF;AACF,IAAI,KAAK;AAAA,EACL,uBAAuB;AAAA,IACrB,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,sBAAsB;AAAA,IACpB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,yBAAyB;AAAA,IACvB,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,0BAA0B;AAAA,IACxB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,wBAAwB;AAAA,IACtB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,gCAAgC;AAAA,IAC9B,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AACF;AACF,IAAI,KAAK,oBAAI,IAAI,CAAC,CAAC,WAAW;AAAA,EAC1B,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,SAAS,WAAW,SAAS,QAAQ,QAAQ;AAAA,EACxD,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,SAAS;AAAA,EACZ,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,SAAS,WAAW,SAAS,MAAM;AAAA,EAC9C,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,WAAW;AAAA,EACd,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,SAAS,WAAW,SAAS,MAAM;AAAA,EAC9C,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,iBAAiB;AAAA,EACpB,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,SAAS,WAAW,SAAS,MAAM;AAAA,EAC9C,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,CAAC,CAAC;AAxBL,IAyBE,KAAK;AAAA,EACH,SAAS;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,EACV;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AACF;AACF,IAAI,KAAK,CAAC,OAAO,UAAU,QAAQ,gBAAgB;AAAnD,IACE,KAAK;AAAA,EACH,KAAK;AAAA,IACH,KAAK;AAAA,IACL,UAAU;AAAA,EACZ;AAAA,EACA,eAAe;AAAA,IACb,MAAM;AAAA,IACN,kBAAkB;AAAA,EACpB;AACF;AAVF,IAWE,KAAK,CAAC,KAAK;AACb,IAAI,KAAK;AAAA,EACP,KAAK;AAAA,IACH,UAAU;AAAA,EACZ;AAAA,EACA,eAAe;AAAA,IACb,kBAAkB;AAAA,EACpB;AACF;AACA,IAAI,KAAK,CAAC,OAAO,MAAM;AAAvB,IACE,KAAK;AAAA,EACH,KAAK;AAAA,IACH,KAAK;AAAA,EACP;AAAA,EACA,eAAe;AAAA,IACb,MAAM;AAAA,EACR;AACF;AACF,IAAI,KAAK;AAAA,EACL,KAAK;AAAA,IACH,KAAK;AAAA,EACP;AAAA,EACA,eAAe;AAAA,IACb,eAAe;AAAA,EACjB;AACF;AAEF,IAAI,MAAM;AAAA,EACN,OAAO;AAAA,EACP,cAAc;AAAA,EACd,SAAS;AAAA,EACT,WAAW;AACb;AALF,IAME,MAAM,CAAC,cAAc,cAAc,YAAY,kBAAkB;AACnE,IAAI,OAAO,CAAC,OAAO,UAAU,QAAQ,gBAAgB;AACrD,IAAI,KAAK;AAAA,EACL,oBAAoB;AAAA,IAClB,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,4BAA4B;AAAA,IAC1B,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AACF;AACF,IAAI,KAAK;AAAA,EACL,SAAS;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AAAA,EACA,SAAS;AAAA,IACP,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AAAA,EACA,iBAAiB;AAAA,IACf,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACF;AA1BF,IA2BE,MAAM;AAAA,EACJ,SAAS,CAAC,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3C,SAAS,CAAC,QAAQ,QAAQ,MAAM;AAAA,EAChC,OAAO,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAAA,EACtC,iBAAiB,CAAC,SAAS,SAAS,SAAS,OAAO;AACtD;AAhCF,IAiCE,KAAK;AAAA,EACH,SAAS;AAAA,IACP,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,EACT;AACF;AA3DF,IA4DE,IAAI,CAAC,YAAY,cAAc,YAAY,WAAW,cAAc,WAAW;AA5DjF,IA6DE,KAAK,CAAC,MAAM,OAAO,OAAO,OAAO,OAAO,OAAO,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,SAAS,SAAS,SAAS,GAAG,KAAK,GAAG,CAAC;AA7DhK,IA8DE,MAAM,CAAC,SAAS,WAAW,SAAS,QAAQ,WAAW,QAAQ;AA9DjE,IA+DE,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AA/DtC,IAgEE,MAAM,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AAhE3D,IAiEE,KAAK,CAAC,GAAG,OAAO,KAAK,GAAG,GAAG,GAAG,KAAK,OAAO,MAAM,MAAM,MAAM,MAAM,OAAO,QAAQ,UAAU,QAAQ,aAAa,UAAU,aAAa,mBAAmB,iBAAiB,QAAQ,MAAM,WAAW,kBAAkB,eAAe,UAAU,MAAM,aAAa,cAAc,SAAS,cAAc,cAAc,aAAa,aAAa,SAAS,cAAc,gBAAgB,QAAQ,YAAY,YAAY,SAAS,MAAM,IAAI,OAAO,IAAI,cAAc,IAAI,SAAS,IAAI,SAAS,EAAE,OAAO,IAAI,IAAI,OAAK,GAAG,OAAO,GAAG,GAAG,CAAC,CAAC,EAAE,OAAO,IAAI,IAAI,OAAK,KAAK,OAAO,CAAC,CAAC,CAAC;AAC3iB,IAAI,KAAK;AAAA,EACL,uBAAuB;AAAA,IACrB,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,sBAAsB;AAAA,IACpB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,KAAK;AAAA,EACP;AAAA,EACA,yBAAyB;AAAA,IACvB,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,0BAA0B;AAAA,IACxB,KAAK;AAAA,EACP;AACF;AAEF,IAAM,uBAAuB;AAC7B,IAAM,gBAAgB;AACtB,IAAM,qBAAqB;AAC3B,IAAM,4BAA4B;AAClC,IAAM,gBAAgB;AACtB,IAAM,yBAAyB;AAC/B,IAAM,iCAAiC;AACvC,IAAM,cAAc;AACpB,IAAM,YAAY;AAClB,IAAM,8BAA8B;AACpC,IAAM,0BAA0B;AAChC,IAAM,sCAAsC,CAAC,QAAQ,QAAQ,SAAS,QAAQ;AAC9E,IAAM,cAAc,MAAM;AACxB,MAAI;AACF,WAAO;AAAA,EACT,SAAS,MAAM;AACb,WAAO;AAAA,EACT;AACF,GAAG;AACH,SAAS,YAAY,KAAK;AAExB,SAAO,IAAI,MAAM,KAAK;AAAA,IACpB,IAAI,QAAQ,MAAM;AAChB,aAAO,QAAQ,SAAS,OAAO,IAAI,IAAI,OAAO,CAAC;AAAA,IACjD;AAAA,EACF,CAAC;AACH;AACA,IAAM,mBAAmB,eAAe,CAAC,GAAG,CAAC;AAK7C,iBAAiB,CAAC,IAAI,eAAe,eAAe,eAAe,eAAe,CAAC,GAAG;AAAA,EACpF,cAAc;AAChB,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,aAAa,CAAC;AACxC,IAAM,kBAAkB,YAAY,gBAAgB;AACpD,IAAM,mBAAmB,eAAe,CAAC,GAAG,EAAE;AAI9C,iBAAiB,CAAC,IAAI,eAAe,eAAe,eAAe,eAAe,CAAC,GAAG;AAAA,EACpF,SAAS;AACX,CAAC,GAAG,iBAAiB,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,aAAa,CAAC;AACvD,IAAM,kBAAkB,YAAY,gBAAgB;AACpD,IAAM,wBAAwB,eAAe,CAAC,GAAG,EAAE;AACnD,sBAAsB,CAAC,IAAI,eAAe,eAAe,CAAC,GAAG,sBAAsB,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC;AACjG,IAAM,uBAAuB,YAAY,qBAAqB;AAC9D,IAAM,wBAAwB,eAAe,CAAC,GAAG,EAAE;AACnD,sBAAsB,CAAC,IAAI,eAAe,eAAe,CAAC,GAAG,sBAAsB,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC;AACjG,IAAM,uBAAuB,YAAY,qBAAqB;AAC9D,IAAM,gCAAgC;AAEtC,IAAM,wBAAwB;AAC9B,IAAM,sBAAsB;AAC5B,IAAM,yBAAyB,eAAe,CAAC,GAAG,CAAC;AACnD,IAAM,wBAAwB,YAAY,sBAAsB;AAChE,IAAM,kCAAkC,CAAC,SAAS,eAAe,aAAa,qBAAqB,cAAc;AACjH,IAAM,kBAAkB;AACxB,IAAM,mBAAmB,CAAC,GAAG,IAAI,GAAG,EAAE;AAEtC,IAAM,UAAU,OAAO,qBAAqB,CAAC;AAC7C,SAAS,cAAc,MAAM;AAC3B,MAAI,UAAU,SAAS,cAAc,YAAY,OAAO,GAAG;AAC3D,MAAI,SAAS;AACX,WAAO,QAAQ,aAAa,IAAI;AAAA,EAClC;AACF;AACA,SAAS,OAAO,KAAK;AAGnB,MAAI,QAAQ,GAAI,QAAO;AACvB,MAAI,QAAQ,QAAS,QAAO;AAC5B,MAAI,QAAQ,OAAQ,QAAO;AAC3B,SAAO;AACT;AACA,IAAI,YAAY,OAAO,SAAS,kBAAkB,YAAY;AAC5D,QAAM,QAAQ,CAAC,CAAC,sBAAsB,cAAc,GAAG,CAAC,mBAAmB,WAAW,GAAG,CAAC,uBAAuB,eAAe,GAAG,CAAC,sBAAsB,cAAc,GAAG,CAAC,0BAA0B,kBAAkB,GAAG,CAAC,yBAAyB,gBAAgB,GAAG,CAAC,qBAAqB,YAAY,GAAG,CAAC,kBAAkB,UAAU,GAAG,CAAC,+BAA+B,sBAAsB,GAAG,CAAC,0BAA0B,kBAAkB,GAAG,CAAC,wBAAwB,gBAAgB,GAAG,CAAC,6BAA6B,oBAAoB,GAAG,CAAC,4BAA4B,oBAAoB,GAAG,CAAC,2BAA2B,kBAAkB,CAAC;AAC1nB,QAAM,QAAQ,UAAQ;AACpB,QAAI,CAAC,MAAM,GAAG,IAAI;AAClB,UAAM,MAAM,OAAO,cAAc,IAAI,CAAC;AACtC,QAAI,QAAQ,UAAa,QAAQ,MAAM;AACrC,cAAQ,GAAG,IAAI;AAAA,IACjB;AAAA,EACF,CAAC;AACH;AACA,IAAM,WAAW;AAAA,EACf,cAAc;AAAA,EACd,eAAe;AAAA,EACf,WAAW;AAAA,EACX,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,kBAAkB;AACpB;AAGA,IAAI,QAAQ,cAAc;AACxB,UAAQ,YAAY,QAAQ;AAC9B;AACA,IAAM,UAAU,eAAe,eAAe,CAAC,GAAG,QAAQ,GAAG,OAAO;AACpE,IAAI,CAAC,QAAQ,eAAgB,SAAQ,mBAAmB;AACxD,IAAM,SAAS,CAAC;AAChB,OAAO,KAAK,QAAQ,EAAE,QAAQ,SAAO;AACnC,SAAO,eAAe,QAAQ,KAAK;AAAA,IACjC,YAAY;AAAA,IACZ,KAAK,SAAU,KAAK;AAClB,cAAQ,GAAG,IAAI;AACf,kBAAY,QAAQ,QAAM,GAAG,MAAM,CAAC;AAAA,IACtC;AAAA,IACA,KAAK,WAAY;AACf,aAAO,QAAQ,GAAG;AAAA,IACpB;AAAA,EACF,CAAC;AACH,CAAC;AAGD,OAAO,eAAe,QAAQ,gBAAgB;AAAA,EAC5C,YAAY;AAAA,EACZ,KAAK,SAAU,KAAK;AAClB,YAAQ,YAAY;AACpB,gBAAY,QAAQ,QAAM,GAAG,MAAM,CAAC;AAAA,EACtC;AAAA,EACA,KAAK,WAAY;AACf,WAAO,QAAQ;AAAA,EACjB;AACF,CAAC;AACD,OAAO,oBAAoB;AAC3B,IAAM,cAAc,CAAC;AACrB,SAAS,SAAS,IAAI;AACpB,cAAY,KAAK,EAAE;AACnB,SAAO,MAAM;AACX,gBAAY,OAAO,YAAY,QAAQ,EAAE,GAAG,CAAC;AAAA,EAC/C;AACF;AAEA,IAAM,MAAM;AACZ,IAAM,uBAAuB;AAAA,EAC3B,MAAM;AAAA,EACN,GAAG;AAAA,EACH,GAAG;AAAA,EACH,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AACT;AACA,SAAS,UAAUC,MAAK;AACtB,MAAI,CAACA,QAAO,CAAC,QAAQ;AACnB;AAAA,EACF;AACA,QAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,QAAM,aAAa,QAAQ,UAAU;AACrC,QAAM,YAAYA;AAClB,QAAM,eAAe,SAAS,KAAK;AACnC,MAAI,cAAc;AAClB,WAAS,IAAI,aAAa,SAAS,GAAG,IAAI,IAAI,KAAK;AACjD,UAAM,QAAQ,aAAa,CAAC;AAC5B,UAAM,WAAW,MAAM,WAAW,IAAI,YAAY;AAClD,QAAI,CAAC,SAAS,MAAM,EAAE,QAAQ,OAAO,IAAI,IAAI;AAC3C,oBAAc;AAAA,IAChB;AAAA,EACF;AACA,WAAS,KAAK,aAAa,OAAO,WAAW;AAC7C,SAAOA;AACT;AACA,IAAM,SAAS;AACf,SAAS,eAAe;AACtB,MAAI,OAAO;AACX,MAAI,KAAK;AACT,SAAO,SAAS,GAAG;AACjB,UAAM,OAAO,KAAK,OAAO,IAAI,KAAK,CAAC;AAAA,EACrC;AACA,SAAO;AACT;AACA,SAAS,QAAQ,KAAK;AACpB,QAAM,QAAQ,CAAC;AACf,WAAS,KAAK,OAAO,CAAC,GAAG,WAAW,GAAG,OAAM;AAC3C,UAAM,CAAC,IAAI,IAAI,CAAC;AAAA,EAClB;AACA,SAAO;AACT;AACA,SAAS,WAAW,MAAM;AACxB,MAAI,KAAK,WAAW;AAClB,WAAO,QAAQ,KAAK,SAAS;AAAA,EAC/B,OAAO;AACL,YAAQ,KAAK,aAAa,OAAO,KAAK,IAAI,MAAM,GAAG,EAAE,OAAO,OAAK,CAAC;AAAA,EACpE;AACF;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,GAAG,OAAO,GAAG,EAAE,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,MAAM;AACxI;AACA,SAAS,eAAe,YAAY;AAClC,SAAO,OAAO,KAAK,cAAc,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,kBAAkB;AAClE,WAAO,MAAM,GAAG,OAAO,eAAe,IAAK,EAAE,OAAO,WAAW,WAAW,aAAa,CAAC,GAAG,IAAK;AAAA,EAClG,GAAG,EAAE,EAAE,KAAK;AACd;AACA,SAAS,WAAWC,SAAQ;AAC1B,SAAO,OAAO,KAAKA,WAAU,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,cAAc;AAC1D,WAAO,MAAM,GAAG,OAAO,WAAW,IAAI,EAAE,OAAOA,QAAO,SAAS,EAAE,KAAK,GAAG,GAAG;AAAA,EAC9E,GAAG,EAAE;AACP;AACA,SAAS,sBAAsB,WAAW;AACxC,SAAO,UAAU,SAAS,qBAAqB,QAAQ,UAAU,MAAM,qBAAqB,KAAK,UAAU,MAAM,qBAAqB,KAAK,UAAU,WAAW,qBAAqB,UAAU,UAAU,SAAS,UAAU;AAC9N;AACA,SAAS,gBAAgB,MAAM;AAC7B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,WAAW,aAAa,OAAO,iBAAiB,GAAG,OAAO;AAAA,EAC5D;AACA,QAAM,iBAAiB,aAAa,OAAO,UAAU,IAAI,IAAI,IAAI,EAAE,OAAO,UAAU,IAAI,IAAI,IAAI;AAChG,QAAM,aAAa,SAAS,OAAO,UAAU,OAAO,MAAM,UAAU,QAAQ,KAAK,IAAI,IAAI,EAAE,OAAO,UAAU,OAAO,MAAM,UAAU,QAAQ,KAAK,IAAI,IAAI;AACxJ,QAAM,cAAc,UAAU,OAAO,UAAU,QAAQ,OAAO;AAC9D,QAAM,QAAQ;AAAA,IACZ,WAAW,GAAG,OAAO,gBAAgB,GAAG,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,WAAW;AAAA,EACtF;AACA,QAAM,OAAO;AAAA,IACX,WAAW,aAAa,OAAO,YAAY,IAAI,IAAI,QAAQ;AAAA,EAC7D;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,gBAAgB,OAAO;AAC9B,MAAI;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,gBAAgB;AAAA,EAClB,IAAI;AACJ,MAAI,MAAM;AACV,MAAI,iBAAiB,OAAO;AAC1B,WAAO,aAAa,OAAO,UAAU,IAAI,MAAM,QAAQ,GAAG,MAAM,EAAE,OAAO,UAAU,IAAI,MAAM,SAAS,GAAG,MAAM;AAAA,EACjH,WAAW,eAAe;AACxB,WAAO,yBAAyB,OAAO,UAAU,IAAI,KAAK,mBAAmB,EAAE,OAAO,UAAU,IAAI,KAAK,OAAO;AAAA,EAClH,OAAO;AACL,WAAO,aAAa,OAAO,UAAU,IAAI,KAAK,MAAM,EAAE,OAAO,UAAU,IAAI,KAAK,MAAM;AAAA,EACxF;AACA,SAAO,SAAS,OAAO,UAAU,OAAO,OAAO,UAAU,QAAQ,KAAK,IAAI,IAAI,EAAE,OAAO,UAAU,OAAO,OAAO,UAAU,QAAQ,KAAK,IAAI,IAAI;AAC9I,SAAO,UAAU,OAAO,UAAU,QAAQ,OAAO;AACjD,SAAO;AACT;AAEA,IAAI,aAAa;AAEjB,SAAS,MAAM;AACb,QAAM,MAAM;AACZ,QAAM,MAAM;AACZ,QAAM,KAAK,OAAO;AAClB,QAAM,KAAK,OAAO;AAClB,MAAIC,KAAI;AACR,MAAI,OAAO,OAAO,OAAO,KAAK;AAC5B,UAAM,QAAQ,IAAI,OAAO,MAAM,OAAO,KAAK,KAAK,GAAG,GAAG;AACtD,UAAM,iBAAiB,IAAI,OAAO,OAAO,OAAO,KAAK,KAAK,GAAG,GAAG;AAChE,UAAM,QAAQ,IAAI,OAAO,MAAM,OAAO,GAAG,GAAG,GAAG;AAC/C,IAAAA,KAAIA,GAAE,QAAQ,OAAO,IAAI,OAAO,IAAI,GAAG,CAAC,EAAE,QAAQ,gBAAgB,KAAK,OAAO,IAAI,GAAG,CAAC,EAAE,QAAQ,OAAO,IAAI,OAAO,EAAE,CAAC;AAAA,EACvH;AACA,SAAOA;AACT;AACA,IAAI,eAAe;AACnB,SAAS,YAAY;AACnB,MAAI,OAAO,cAAc,CAAC,cAAc;AACtC,cAAU,IAAI,CAAC;AACf,mBAAe;AAAA,EACjB;AACF;AACA,IAAI,YAAY;AAAA,EACd,SAAS;AACP,WAAO;AAAA,MACL,KAAK;AAAA,QACH;AAAA,QACA,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AACN,WAAO;AAAA,MACL,2BAA2B;AACzB,kBAAU;AAAA,MACZ;AAAA,MACA,cAAc;AACZ,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,IAAI,UAAU,CAAC;AACrB,IAAI,CAAC,EAAE,oBAAoB,EAAG,GAAE,oBAAoB,IAAI,CAAC;AACzD,IAAI,CAAC,EAAE,oBAAoB,EAAE,OAAQ,GAAE,oBAAoB,EAAE,SAAS,CAAC;AACvE,IAAI,CAAC,EAAE,oBAAoB,EAAE,MAAO,GAAE,oBAAoB,EAAE,QAAQ,CAAC;AACrE,IAAI,CAAC,EAAE,oBAAoB,EAAE,MAAO,GAAE,oBAAoB,EAAE,QAAQ,CAAC;AACrE,IAAI,YAAY,EAAE,oBAAoB;AAEtC,IAAM,YAAY,CAAC;AACnB,IAAM,WAAW,WAAY;AAC3B,WAAS,oBAAoB,oBAAoB,QAAQ;AACzD,WAAS;AACT,YAAU,IAAI,QAAM,GAAG,CAAC;AAC1B;AACA,IAAI,SAAS;AACb,IAAI,QAAQ;AACV,YAAU,SAAS,gBAAgB,WAAW,eAAe,iBAAiB,KAAK,SAAS,UAAU;AACtG,MAAI,CAAC,OAAQ,UAAS,iBAAiB,oBAAoB,QAAQ;AACrE;AACA,SAAS,SAAU,IAAI;AACrB,MAAI,CAAC,OAAQ;AACb,WAAS,WAAW,IAAI,CAAC,IAAI,UAAU,KAAK,EAAE;AAChD;AAEA,SAAS,OAAO,eAAe;AAC7B,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC;AAAA,IACd,WAAW,CAAC;AAAA,EACd,IAAI;AACJ,MAAI,OAAO,kBAAkB,UAAU;AACrC,WAAO,WAAW,aAAa;AAAA,EACjC,OAAO;AACL,WAAO,IAAI,OAAO,KAAK,GAAG,EAAE,OAAO,eAAe,UAAU,GAAG,GAAG,EAAE,OAAO,SAAS,IAAI,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,OAAO,KAAK,GAAG;AAAA,EACjI;AACF;AAEA,SAAS,gBAAgB,SAAS,QAAQ,UAAU;AAClD,MAAI,WAAW,QAAQ,MAAM,KAAK,QAAQ,MAAM,EAAE,QAAQ,GAAG;AAC3D,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,MAAM,QAAQ,MAAM,EAAE,QAAQ;AAAA,IAChC;AAAA,EACF;AACF;AAMA,IAAI,gBAAgB,SAASC,eAAc,MAAM,aAAa;AAC5D,SAAO,SAAU,GAAG,GAAG,GAAG,GAAG;AAC3B,WAAO,KAAK,KAAK,aAAa,GAAG,GAAG,GAAG,CAAC;AAAA,EAC1C;AACF;AAaA,IAAI,SAAS,SAAS,iBAAiB,SAAS,IAAI,cAAc,aAAa;AAC7E,MAAI,OAAO,OAAO,KAAK,OAAO,GAC5B,SAAS,KAAK,QACd,WAAW,gBAAgB,SAAY,cAAc,IAAI,WAAW,IAAI,IACxE,GACA,KACA;AACF,MAAI,iBAAiB,QAAW;AAC9B,QAAI;AACJ,aAAS,QAAQ,KAAK,CAAC,CAAC;AAAA,EAC1B,OAAO;AACL,QAAI;AACJ,aAAS;AAAA,EACX;AACA,SAAO,IAAI,QAAQ,KAAK;AACtB,UAAM,KAAK,CAAC;AACZ,aAAS,SAAS,QAAQ,QAAQ,GAAG,GAAG,KAAK,OAAO;AAAA,EACtD;AACA,SAAO;AACT;AA2BA,SAAS,WAAW,QAAQ;AAC1B,QAAM,SAAS,CAAC;AAChB,MAAIC,WAAU;AACd,QAAM,SAAS,OAAO;AACtB,SAAOA,WAAU,QAAQ;AACvB,UAAM,QAAQ,OAAO,WAAWA,UAAS;AACzC,QAAI,SAAS,SAAU,SAAS,SAAUA,WAAU,QAAQ;AAC1D,YAAM,QAAQ,OAAO,WAAWA,UAAS;AACzC,WAAK,QAAQ,UAAW,OAAQ;AAE9B,eAAO,OAAO,QAAQ,SAAU,OAAO,QAAQ,QAAS,KAAO;AAAA,MACjE,OAAO;AACL,eAAO,KAAK,KAAK;AACjB,QAAAA;AAAA,MACF;AAAA,IACF,OAAO;AACL,aAAO,KAAK,KAAK;AAAA,IACnB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,MAAM,SAAS;AACtB,QAAM,UAAU,WAAW,OAAO;AAClC,SAAO,QAAQ,WAAW,IAAI,QAAQ,CAAC,EAAE,SAAS,EAAE,IAAI;AAC1D;AACA,SAAS,YAAY,QAAQ,OAAO;AAClC,QAAM,OAAO,OAAO;AACpB,MAAI,QAAQ,OAAO,WAAW,KAAK;AACnC,MAAI;AACJ,MAAI,SAAS,SAAU,SAAS,SAAU,OAAO,QAAQ,GAAG;AAC1D,aAAS,OAAO,WAAW,QAAQ,CAAC;AACpC,QAAI,UAAU,SAAU,UAAU,OAAQ;AACxC,cAAQ,QAAQ,SAAU,OAAQ,SAAS,QAAS;AAAA,IACtD;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,eAAe,OAAO;AAC7B,SAAO,OAAO,KAAK,KAAK,EAAE,OAAO,CAAC,KAAK,aAAa;AAClD,UAAMC,QAAO,MAAM,QAAQ;AAC3B,UAAM,WAAW,CAAC,CAACA,MAAK;AACxB,QAAI,UAAU;AACZ,UAAIA,MAAK,QAAQ,IAAIA,MAAK;AAAA,IAC5B,OAAO;AACL,UAAI,QAAQ,IAAIA;AAAA,IAClB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,SAAS,YAAY,QAAQ,OAAO;AAClC,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,QAAM;AAAA,IACJ,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,aAAa,eAAe,KAAK;AACvC,MAAI,OAAO,UAAU,MAAM,YAAY,cAAc,CAAC,WAAW;AAC/D,cAAU,MAAM,QAAQ,QAAQ,eAAe,KAAK,CAAC;AAAA,EACvD,OAAO;AACL,cAAU,OAAO,MAAM,IAAI,eAAe,eAAe,CAAC,GAAG,UAAU,OAAO,MAAM,KAAK,CAAC,CAAC,GAAG,UAAU;AAAA,EAC1G;AAQA,MAAI,WAAW,OAAO;AACpB,gBAAY,MAAM,KAAK;AAAA,EACzB;AACF;AAEA,IAAM,gBAAgB,CAAc,YAAY,sCAAsC;AAAA,EACpF,IAAI;AAAA,EACJ,IAAI;AACN,CAAC,GAAgB,YAAY,wEAAwE;AAAA,EACnG,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,IAAI;AACN,CAAC,GAAgB,YAAY,qCAAqC;AAAA,EAChE,MAAM;AAAA,EACN,IAAI;AACN,CAAC,CAAC;AAEF,IAAM;AAAA,EACJ;AAAA,EACA;AACF,IAAI;AACJ,IAAM,eAAe,OAAO,KAAK,oBAAoB;AACrD,IAAM,sBAAsB,aAAa,OAAO,CAAC,KAAK,aAAa;AACjE,MAAI,QAAQ,IAAI,OAAO,KAAK,qBAAqB,QAAQ,CAAC;AAC1D,SAAO;AACT,GAAG,CAAC,CAAC;AACL,IAAI,uBAAuB;AAC3B,IAAI,aAAa,CAAC;AAClB,IAAI,cAAc,CAAC;AACnB,IAAI,aAAa,CAAC;AAClB,IAAI,gBAAgB,CAAC;AACrB,IAAI,WAAW,CAAC;AAChB,SAAS,WAAW,MAAM;AACxB,SAAO,CAAC,iBAAiB,QAAQ,IAAI;AACvC;AACA,SAAS,YAAY,WAAW,KAAK;AACnC,QAAM,QAAQ,IAAI,MAAM,GAAG;AAC3B,QAAM,SAAS,MAAM,CAAC;AACtB,QAAM,WAAW,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AACxC,MAAI,WAAW,aAAa,aAAa,MAAM,CAAC,WAAW,QAAQ,GAAG;AACpE,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,IAAM,QAAQ,MAAM;AAClB,QAAM,SAAS,aAAW;AACxB,WAAO,OAAO,QAAQ,CAAC,MAAM,OAAO,WAAW;AAC7C,WAAK,MAAM,IAAI,OAAO,OAAO,SAAS,CAAC,CAAC;AACxC,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AACA,eAAa,OAAO,CAAC,KAAKA,OAAM,aAAa;AAC3C,QAAIA,MAAK,CAAC,GAAG;AACX,UAAIA,MAAK,CAAC,CAAC,IAAI;AAAA,IACjB;AACA,QAAIA,MAAK,CAAC,GAAG;AACX,YAAM,UAAUA,MAAK,CAAC,EAAE,OAAO,UAAQ;AACrC,eAAO,OAAO,SAAS;AAAA,MACzB,CAAC;AACD,cAAQ,QAAQ,WAAS;AACvB,YAAI,MAAM,SAAS,EAAE,CAAC,IAAI;AAAA,MAC5B,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,CAAC;AACD,gBAAc,OAAO,CAAC,KAAKA,OAAM,aAAa;AAC5C,QAAI,QAAQ,IAAI;AAChB,QAAIA,MAAK,CAAC,GAAG;AACX,YAAM,UAAUA,MAAK,CAAC,EAAE,OAAO,UAAQ;AACrC,eAAO,OAAO,SAAS;AAAA,MACzB,CAAC;AACD,cAAQ,QAAQ,WAAS;AACvB,YAAI,KAAK,IAAI;AAAA,MACf,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,CAAC;AACD,aAAW,OAAO,CAAC,KAAKA,OAAM,aAAa;AACzC,UAAM,UAAUA,MAAK,CAAC;AACtB,QAAI,QAAQ,IAAI;AAChB,YAAQ,QAAQ,WAAS;AACvB,UAAI,KAAK,IAAI;AAAA,IACf,CAAC;AACD,WAAO;AAAA,EACT,CAAC;AAID,QAAM,aAAa,SAAS,UAAU,OAAO;AAC7C,QAAM,cAAc,OAAO,OAAO,CAAC,KAAK,SAAS;AAC/C,UAAM,wBAAwB,KAAK,CAAC;AACpC,QAAI,SAAS,KAAK,CAAC;AACnB,UAAM,WAAW,KAAK,CAAC;AACvB,QAAI,WAAW,SAAS,CAAC,YAAY;AACnC,eAAS;AAAA,IACX;AACA,QAAI,OAAO,0BAA0B,UAAU;AAC7C,UAAI,MAAM,qBAAqB,IAAI;AAAA,QACjC;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,0BAA0B,UAAU;AAC7C,UAAI,SAAS,sBAAsB,SAAS,EAAE,CAAC,IAAI;AAAA,QACjD;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,IACR,UAAU,CAAC;AAAA,EACb,CAAC;AACD,eAAa,YAAY;AACzB,kBAAgB,YAAY;AAC5B,yBAAuB,mBAAmB,OAAO,cAAc;AAAA,IAC7D,QAAQ,OAAO;AAAA,EACjB,CAAC;AACH;AACA,SAAS,UAAQ;AACf,yBAAuB,mBAAmB,KAAK,cAAc;AAAA,IAC3D,QAAQ,OAAO;AAAA,EACjB,CAAC;AACH,CAAC;AACD,MAAM;AACN,SAAS,UAAU,QAAQ,SAAS;AAClC,UAAQ,WAAW,MAAM,KAAK,CAAC,GAAG,OAAO;AAC3C;AACA,SAAS,WAAW,QAAQ,UAAU;AACpC,UAAQ,YAAY,MAAM,KAAK,CAAC,GAAG,QAAQ;AAC7C;AACA,SAAS,QAAQ,QAAQ,OAAO;AAC9B,UAAQ,SAAS,MAAM,KAAK,CAAC,GAAG,KAAK;AACvC;AACA,SAAS,UAAU,MAAM;AACvB,SAAO,WAAW,IAAI,KAAK;AAAA,IACzB,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AACF;AACA,SAAS,aAAa,SAAS;AAC7B,QAAM,aAAa,cAAc,OAAO;AACxC,QAAM,aAAa,UAAU,OAAO,OAAO;AAC3C,SAAO,eAAe,aAAa;AAAA,IACjC,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ,IAAI,SAAS;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AACF;AACA,SAAS,yBAAyB;AAChC,SAAO;AACT;AACA,IAAM,qBAAqB,MAAM;AAC/B,SAAO;AAAA,IACL,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,MAAM,CAAC;AAAA,EACT;AACF;AACA,SAAS,YAAY,QAAQ;AAC3B,MAAI,SAAS;AACb,QAAM,WAAW,aAAa,OAAO,CAAC,KAAK,aAAa;AACtD,QAAI,QAAQ,IAAI,GAAG,OAAO,OAAO,WAAW,GAAG,EAAE,OAAO,QAAQ;AAChE,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,IAAE,QAAQ,cAAY;AACpB,QAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,KAAK,OAAO,KAAK,UAAQ,oBAAoB,QAAQ,EAAE,SAAS,IAAI,CAAC,GAAG;AAC5G,eAAS;AAAA,IACX;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,mBAAmB,eAAe;AACzC,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,QAAM;AAAA,IACJ,SAAS;AAAA,EACX,IAAI;AACJ,QAAM,QAAQ,gBAAgB,MAAM,EAAE,aAAa;AAGnD,MAAI,WAAW,KAAK,CAAC,eAAe;AAClC,WAAO;AAAA,EACT;AACA,QAAM,SAAS,gBAAgB,MAAM,EAAE,aAAa,KAAK,gBAAgB,MAAM,EAAE,KAAK;AACtF,QAAM,UAAU,iBAAiB,UAAU,SAAS,gBAAgB;AACpE,QAAM,SAAS,UAAU,WAAW;AACpC,SAAO;AACT;AACA,SAAS,uBAAuB,YAAY;AAC1C,MAAI,OAAO,CAAC;AACZ,MAAI,WAAW;AACf,aAAW,QAAQ,SAAO;AACxB,UAAM,SAAS,YAAY,OAAO,WAAW,GAAG;AAChD,QAAI,QAAQ;AACV,iBAAW;AAAA,IACb,WAAW,KAAK;AACd,WAAK,KAAK,GAAG;AAAA,IACf;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,KAAK;AAC/B,SAAO,IAAI,KAAK,EAAE,OAAO,CAAC,OAAO,OAAOC,SAAQ;AAC9C,WAAOA,KAAI,QAAQ,KAAK,MAAM;AAAA,EAChC,CAAC;AACH;AACA,SAAS,iBAAiB,QAAQ;AAChC,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,QAAM;AAAA,IACJ,cAAc;AAAA,EAChB,IAAI;AACJ,MAAI,cAAc;AAClB,QAAM,oBAAoB,GAAG,OAAO,IAAI;AACxC,QAAM,yBAAyB,mBAAmB,OAAO,OAAO,SAAO,kBAAkB,SAAS,GAAG,CAAC,CAAC;AACvG,QAAM,0BAA0B,mBAAmB,OAAO,OAAO,SAAO,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC;AAC1F,QAAM,WAAW,uBAAuB,OAAO,SAAO;AACpD,kBAAc;AACd,WAAO,CAAC,EAAE,SAAS,GAAG;AAAA,EACxB,CAAC;AACD,QAAM,CAAC,kBAAkB,IAAI,IAAI;AACjC,QAAM,SAAS,YAAY,sBAAsB;AACjD,QAAM,YAAY,eAAe,eAAe,CAAC,GAAG,uBAAuB,uBAAuB,CAAC,GAAG,CAAC,GAAG;AAAA,IACxG,QAAQ,mBAAmB,iBAAiB;AAAA,MAC1C;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,SAAO,eAAe,eAAe,eAAe,CAAC,GAAG,SAAS,GAAG,0BAA0B;AAAA,IAC5F;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC,GAAG,kBAAkB,aAAa,aAAa,SAAS,CAAC;AAC7D;AACA,SAAS,kBAAkB,aAAa,aAAa,WAAW;AAC9D,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,eAAe,CAAC,UAAU,CAAC,UAAU;AACvC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,OAAO,gBAAgB,OAAO,UAAU,QAAQ,IAAI,CAAC;AAC3D,QAAM,gBAAgB,QAAQ,QAAQ,QAAQ;AAC9C,aAAW,KAAK,YAAY,iBAAiB;AAC7C,WAAS,KAAK,UAAU;AACxB,MAAI,WAAW,SAAS,CAAC,OAAO,KAAK,KAAK,OAAO,KAAK,KAAK,CAAC,OAAO,cAAc;AAG/E,aAAS;AAAA,EACX;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,uBAAuB,EAAE,OAAO,cAAY;AAChD,SAAO,aAAa,KAAK,aAAa;AACxC,CAAC;AACD,IAAM,qBAAqB,OAAO,KAAK,EAAE,EAAE,OAAO,SAAO,QAAQ,CAAC,EAAE,IAAI,SAAO,OAAO,KAAK,GAAG,GAAG,CAAC,CAAC,EAAE,KAAK;AAC1G,SAAS,0BAA0B,eAAe;AAChD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd,QAAAL,UAAS,CAAC;AAAA,IACV,QAAQ,YAAY,CAAC;AAAA,EACvB,IAAI;AACJ,QAAM,kBAAkB,WAAW;AACnC,QAAM,mBAAmB,OAAO,SAAS,YAAY,KAAK,OAAO,SAAS,KAAK;AAC/E,QAAM,yBAAyB,UAAU,kBAAkB;AAC3D,QAAM,2BAA2B,UAAU,WAAW,SAAS,UAAU,WAAW;AACpF,MAAI,CAAC,oBAAoB,oBAAoB,0BAA0B,2BAA2B;AAChG,cAAU,SAAS;AAAA,EACrB;AACA,MAAI,OAAO,SAAS,WAAW,KAAK,OAAO,SAAS,KAAK,GAAG;AAC1D,cAAU,SAAS;AAAA,EACrB;AACA,MAAI,CAAC,UAAU,UAAU,qBAAqB,SAAS,MAAM,GAAG;AAC9D,UAAM,cAAc,OAAO,KAAKA,OAAM,EAAE,KAAK,SAAO,mBAAmB,SAAS,GAAG,CAAC;AACpF,QAAI,eAAe,UAAU,cAAc;AACzC,YAAM,gBAAgB,GAAG,IAAI,MAAM,EAAE;AACrC,gBAAU,SAAS;AACnB,gBAAU,WAAW,QAAQ,UAAU,QAAQ,UAAU,QAAQ,KAAK,UAAU;AAAA,IAClF;AAAA,EACF;AACA,MAAI,UAAU,WAAW,QAAQ,gBAAgB,MAAM;AAGrD,cAAU,SAAS,uBAAuB,KAAK;AAAA,EACjD;AACA,SAAO;AACT;AAEA,IAAM,UAAN,MAAc;AAAA,EACZ,cAAc;AACZ,SAAK,cAAc,CAAC;AAAA,EACtB;AAAA,EACA,MAAM;AACJ,aAAS,OAAO,UAAU,QAAQ,cAAc,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC9F,kBAAY,IAAI,IAAI,UAAU,IAAI;AAAA,IACpC;AACA,UAAM,YAAY,YAAY,OAAO,KAAK,kBAAkB,CAAC,CAAC;AAC9D,WAAO,KAAK,SAAS,EAAE,QAAQ,SAAO;AACpC,WAAK,YAAY,GAAG,IAAI,eAAe,eAAe,CAAC,GAAG,KAAK,YAAY,GAAG,KAAK,CAAC,CAAC,GAAG,UAAU,GAAG,CAAC;AACtG,kBAAY,KAAK,UAAU,GAAG,CAAC;AAG/B,YAAM,aAAa,qBAAqB,CAAC,EAAE,GAAG;AAC9C,UAAI,WAAY,aAAY,YAAY,UAAU,GAAG,CAAC;AACtD,YAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,QAAQ;AACN,SAAK,cAAc,CAAC;AAAA,EACtB;AAAA,EACA,iBAAiB,WAAW,YAAY;AACtC,UAAM,aAAa,WAAW,UAAU,WAAW,YAAY,WAAW,OAAO;AAAA,MAC/E,GAAG;AAAA,IACL,IAAI;AACJ,WAAO,KAAK,UAAU,EAAE,IAAI,SAAO;AACjC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA,MAAAI;AAAA,MACF,IAAI,WAAW,GAAG;AAClB,YAAM,UAAUA,MAAK,CAAC;AACtB,UAAI,CAAC,UAAU,MAAM,EAAG,WAAU,MAAM,IAAI,CAAC;AAC7C,UAAI,QAAQ,SAAS,GAAG;AACtB,gBAAQ,QAAQ,WAAS;AACvB,cAAI,OAAO,UAAU,UAAU;AAC7B,sBAAU,MAAM,EAAE,KAAK,IAAIA;AAAA,UAC7B;AAAA,QACF,CAAC;AAAA,MACH;AACA,gBAAU,MAAM,EAAE,QAAQ,IAAIA;AAAA,IAChC,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAEA,IAAI,WAAW,CAAC;AAChB,IAAI,SAAS,CAAC;AACd,IAAM,YAAY,CAAC;AACnB,IAAM,sBAAsB,OAAO,KAAK,SAAS;AACjD,SAAS,gBAAgB,aAAa,MAAM;AAC1C,MAAI;AAAA,IACF,WAAW;AAAA,EACb,IAAI;AACJ,aAAW;AACX,WAAS,CAAC;AACV,SAAO,KAAK,SAAS,EAAE,QAAQ,OAAK;AAClC,QAAI,oBAAoB,QAAQ,CAAC,MAAM,IAAI;AACzC,aAAO,UAAU,CAAC;AAAA,IACpB;AAAA,EACF,CAAC;AACD,WAAS,QAAQ,YAAU;AACzB,UAAM,SAAS,OAAO,SAAS,OAAO,OAAO,IAAI,CAAC;AAClD,WAAO,KAAK,MAAM,EAAE,QAAQ,QAAM;AAChC,UAAI,OAAO,OAAO,EAAE,MAAM,YAAY;AACpC,YAAI,EAAE,IAAI,OAAO,EAAE;AAAA,MACrB;AACA,UAAI,OAAO,OAAO,EAAE,MAAM,UAAU;AAClC,eAAO,KAAK,OAAO,EAAE,CAAC,EAAE,QAAQ,QAAM;AACpC,cAAI,CAAC,IAAI,EAAE,GAAG;AACZ,gBAAI,EAAE,IAAI,CAAC;AAAA,UACb;AACA,cAAI,EAAE,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,EAAE;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,QAAI,OAAO,OAAO;AAChB,YAAM,QAAQ,OAAO,MAAM;AAC3B,aAAO,KAAK,KAAK,EAAE,QAAQ,UAAQ;AACjC,YAAI,CAAC,OAAO,IAAI,GAAG;AACjB,iBAAO,IAAI,IAAI,CAAC;AAAA,QAClB;AACA,eAAO,IAAI,EAAE,KAAK,MAAM,IAAI,CAAC;AAAA,MAC/B,CAAC;AAAA,IACH;AACA,QAAI,OAAO,UAAU;AACnB,aAAO,SAAS,SAAS;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,WAAW,MAAM,aAAa;AACrC,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,SAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,EACjC;AACA,QAAM,UAAU,OAAO,IAAI,KAAK,CAAC;AACjC,UAAQ,QAAQ,YAAU;AACxB,kBAAc,OAAO,MAAM,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC;AAAA,EACzD,CAAC;AACD,SAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,WAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,SAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,EACnC;AACA,QAAM,UAAU,OAAO,IAAI,KAAK,CAAC;AACjC,UAAQ,QAAQ,YAAU;AACxB,WAAO,MAAM,MAAM,IAAI;AAAA,EACzB,CAAC;AACD,SAAO;AACT;AACA,SAAS,eAAe;AACtB,QAAM,OAAO,UAAU,CAAC;AACxB,QAAM,OAAO,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AACpD,SAAO,UAAU,IAAI,IAAI,UAAU,IAAI,EAAE,MAAM,MAAM,IAAI,IAAI;AAC/D;AAEA,SAAS,mBAAmB,YAAY;AACtC,MAAI,WAAW,WAAW,MAAM;AAC9B,eAAW,SAAS;AAAA,EACtB;AACA,MAAI;AAAA,IACF;AAAA,EACF,IAAI;AACJ,QAAM,SAAS,WAAW,UAAU,uBAAuB;AAC3D,MAAI,CAAC,SAAU;AACf,aAAW,QAAQ,QAAQ,QAAQ,KAAK;AACxC,SAAO,gBAAgB,QAAQ,aAAa,QAAQ,QAAQ,KAAK,gBAAgB,UAAU,QAAQ,QAAQ,QAAQ;AACrH;AACA,IAAM,UAAU,IAAI,QAAQ;AAC5B,IAAM,SAAS,MAAM;AACnB,SAAO,iBAAiB;AACxB,SAAO,mBAAmB;AAC1B,YAAU,QAAQ;AACpB;AACA,IAAM,MAAM;AAAA,EACV,OAAO,WAAY;AACjB,QAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,QAAI,QAAQ;AACV,gBAAU,eAAe,MAAM;AAC/B,mBAAa,sBAAsB,MAAM;AACzC,aAAO,aAAa,SAAS,MAAM;AAAA,IACrC,OAAO;AACL,aAAO,QAAQ,OAAO,IAAI,MAAM,wCAAwC,CAAC;AAAA,IAC3E;AAAA,EACF;AAAA,EACA,OAAO,WAAY;AACjB,QAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,OAAO,mBAAmB,OAAO;AACnC,aAAO,iBAAiB;AAAA,IAC1B;AACA,WAAO,mBAAmB;AAC1B,aAAS,MAAM;AACb,kBAAY;AAAA,QACV;AAAA,MACF,CAAC;AACD,gBAAU,SAAS,MAAM;AAAA,IAC3B,CAAC;AAAA,EACH;AACF;AACA,IAAM,QAAQ;AAAA,EACZ,MAAM,CAAAA,UAAQ;AACZ,QAAIA,UAAS,MAAM;AACjB,aAAO;AAAA,IACT;AACA,QAAI,OAAOA,UAAS,YAAYA,MAAK,UAAUA,MAAK,UAAU;AAC5D,aAAO;AAAA,QACL,QAAQA,MAAK;AAAA,QACb,UAAU,QAAQA,MAAK,QAAQA,MAAK,QAAQ,KAAKA,MAAK;AAAA,MACxD;AAAA,IACF;AACA,QAAI,MAAM,QAAQA,KAAI,KAAKA,MAAK,WAAW,GAAG;AAC5C,YAAM,WAAWA,MAAK,CAAC,EAAE,QAAQ,KAAK,MAAM,IAAIA,MAAK,CAAC,EAAE,MAAM,CAAC,IAAIA,MAAK,CAAC;AACzE,YAAM,SAAS,mBAAmBA,MAAK,CAAC,CAAC;AACzC,aAAO;AAAA,QACL;AAAA,QACA,UAAU,QAAQ,QAAQ,QAAQ,KAAK;AAAA,MACzC;AAAA,IACF;AACA,QAAI,OAAOA,UAAS,aAAaA,MAAK,QAAQ,GAAG,OAAO,OAAO,WAAW,GAAG,CAAC,IAAI,MAAMA,MAAK,MAAM,6BAA6B,IAAI;AAClI,YAAM,gBAAgB,iBAAiBA,MAAK,MAAM,GAAG,GAAG;AAAA,QACtD,aAAa;AAAA,MACf,CAAC;AACD,aAAO;AAAA,QACL,QAAQ,cAAc,UAAU,uBAAuB;AAAA,QACvD,UAAU,QAAQ,cAAc,QAAQ,cAAc,QAAQ,KAAK,cAAc;AAAA,MACnF;AAAA,IACF;AACA,QAAI,OAAOA,UAAS,UAAU;AAC5B,YAAM,SAAS,uBAAuB;AACtC,aAAO;AAAA,QACL;AAAA,QACA,UAAU,QAAQ,QAAQA,KAAI,KAAKA;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,MAAM;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,cAAc,WAAY;AAC9B,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,QAAM;AAAA,IACJ,qBAAqB;AAAA,EACvB,IAAI;AACJ,OAAK,OAAO,KAAK,UAAU,MAAM,EAAE,SAAS,KAAK,OAAO,iBAAiB,UAAU,OAAO,eAAgB,KAAI,IAAI,MAAM;AAAA,IACtH,MAAM;AAAA,EACR,CAAC;AACH;AAEA,SAAS,YAAY,KAAK,iBAAiB;AACzC,SAAO,eAAe,KAAK,YAAY;AAAA,IACrC,KAAK;AAAA,EACP,CAAC;AACD,SAAO,eAAe,KAAK,QAAQ;AAAA,IACjC,KAAK,WAAY;AACf,aAAO,IAAI,SAAS,IAAI,OAAK,OAAO,CAAC,CAAC;AAAA,IACxC;AAAA,EACF,CAAC;AACD,SAAO,eAAe,KAAK,QAAQ;AAAA,IACjC,KAAK,WAAY;AACf,UAAI,CAAC,OAAQ;AACb,YAAM,YAAY,SAAS,cAAc,KAAK;AAC9C,gBAAU,YAAY,IAAI;AAC1B,aAAO,UAAU;AAAA,IACnB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,SAAS,OAAQ,MAAM;AACrB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAAJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,sBAAsB,SAAS,KAAK,KAAK,SAAS,CAAC,KAAK,OAAO;AACjE,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,SAAS;AAAA,MACb,GAAG,QAAQ,SAAS;AAAA,MACpB,GAAG;AAAA,IACL;AACA,eAAW,OAAO,IAAI,WAAW,eAAe,eAAe,CAAC,GAAGA,OAAM,GAAG,CAAC,GAAG;AAAA,MAC9E,oBAAoB,GAAG,OAAO,OAAO,IAAI,UAAU,IAAI,IAAI,KAAK,EAAE,OAAO,OAAO,IAAI,UAAU,IAAI,IAAI,IAAI;AAAA,IAC5G,CAAC,CAAC;AAAA,EACJ;AACA,SAAO,CAAC;AAAA,IACN,KAAK;AAAA,IACL;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,SAAS,SAAU,MAAM;AACvB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,KAAK,WAAW,OAAO,GAAG,OAAO,QAAQ,GAAG,EAAE,OAAO,OAAO,WAAW,GAAG,EAAE,OAAO,QAAQ,IAAI;AACrG,SAAO,CAAC;AAAA,IACN,KAAK;AAAA,IACL,YAAY;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU,CAAC;AAAA,MACT,KAAK;AAAA,MACL,YAAY,eAAe,eAAe,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG;AAAA,QAC7D;AAAA,MACF,CAAC;AAAA,MACD;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,sBAAsB,QAAQ;AACrC,QAAM;AAAA,IACJ,OAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,EACd,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,KAAK,QAAQ,OAAO;AACxB,QAAM,iBAAiB,GAAG,SAAS,MAAM;AACzC,QAAM,YAAY,CAAC,OAAO,kBAAkB,WAAW,GAAG,OAAO,OAAO,WAAW,GAAG,EAAE,OAAO,QAAQ,IAAI,EAAE,EAAE,OAAO,UAAQ,MAAM,QAAQ,QAAQ,IAAI,MAAM,EAAE,EAAE,OAAO,UAAQ,SAAS,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,MAAM,OAAO,EAAE,KAAK,GAAG;AACtO,MAAI,UAAU;AAAA,IACZ,UAAU,CAAC;AAAA,IACX,YAAY,eAAe,eAAe,CAAC,GAAG,MAAM,UAAU,GAAG,CAAC,GAAG;AAAA,MACnE,eAAe;AAAA,MACf,aAAa;AAAA,MACb,SAAS;AAAA,MACT,QAAQ,MAAM,WAAW,QAAQ;AAAA,MACjC,SAAS;AAAA,MACT,WAAW,OAAO,OAAO,OAAO,GAAG,EAAE,OAAO,MAAM;AAAA,IACpD,CAAC;AAAA,EACH;AACA,QAAM,yBAAyB,kBAAkB,CAAC,CAAC,MAAM,QAAQ,QAAQ,OAAO,IAAI;AAAA,IAClF,OAAO,GAAG,OAAO,QAAQ,SAAS,KAAK,QAAQ,IAAI;AAAA,EACrD,IAAI,CAAC;AACL,MAAI,WAAW;AACb,YAAQ,WAAW,aAAa,IAAI;AAAA,EACtC;AACA,MAAI,OAAO;AACT,YAAQ,SAAS,KAAK;AAAA,MACpB,KAAK;AAAA,MACL,YAAY;AAAA,QACV,IAAI,QAAQ,WAAW,iBAAiB,KAAK,SAAS,OAAO,WAAW,aAAa,CAAC;AAAA,MACxF;AAAA,MACA,UAAU,CAAC,KAAK;AAAA,IAClB,CAAC;AACD,WAAO,QAAQ,WAAW;AAAA,EAC5B;AACA,QAAM,OAAO,eAAe,eAAe,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG;AAAA,IAC3D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ,eAAe,eAAe,CAAC,GAAG,sBAAsB,GAAG,MAAM,MAAM;AAAA,EACjF,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,KAAK,SAAS,KAAK,QAAQ,aAAa,wBAAwB,IAAI,KAAK;AAAA,IAC3E,UAAU,CAAC;AAAA,IACX,YAAY,CAAC;AAAA,EACf,IAAI,aAAa,wBAAwB,IAAI,KAAK;AAAA,IAChD,UAAU,CAAC;AAAA,IACX,YAAY,CAAC;AAAA,EACf;AACA,OAAK,WAAW;AAChB,OAAK,aAAa;AAClB,MAAI,QAAQ;AACV,WAAO,SAAS,IAAI;AAAA,EACtB,OAAO;AACL,WAAO,OAAO,IAAI;AAAA,EACpB;AACF;AACA,SAAS,uBAAuB,QAAQ;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,aAAa,eAAe,eAAe,eAAe,CAAC,GAAG,MAAM,UAAU,GAAG,QAAQ;AAAA,IAC7F,SAAS;AAAA,EACX,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG;AAAA,IACX,SAAS,MAAM,QAAQ,KAAK,GAAG;AAAA,EACjC,CAAC;AACD,MAAI,WAAW;AACb,eAAW,aAAa,IAAI;AAAA,EAC9B;AACA,QAAMA,UAAS,eAAe,CAAC,GAAG,MAAM,MAAM;AAC9C,MAAI,sBAAsB,SAAS,GAAG;AACpC,IAAAA,QAAO,WAAW,IAAI,gBAAgB;AAAA,MACpC;AAAA,MACA,eAAe;AAAA,MACf;AAAA,MACA;AAAA,IACF,CAAC;AACD,IAAAA,QAAO,mBAAmB,IAAIA,QAAO,WAAW;AAAA,EAClD;AACA,QAAM,cAAc,WAAWA,OAAM;AACrC,MAAI,YAAY,SAAS,GAAG;AAC1B,eAAW,OAAO,IAAI;AAAA,EACxB;AACA,QAAM,MAAM,CAAC;AACb,MAAI,KAAK;AAAA,IACP,KAAK;AAAA,IACL;AAAA,IACA,UAAU,CAAC,OAAO;AAAA,EACpB,CAAC;AACD,MAAI,OAAO;AACT,QAAI,KAAK;AAAA,MACP,KAAK;AAAA,MACL,YAAY;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU,CAAC,KAAK;AAAA,IAClB,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,0BAA0B,QAAQ;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,aAAa,eAAe,eAAe,eAAe,CAAC,GAAG,MAAM,UAAU,GAAG,QAAQ;AAAA,IAC7F,SAAS;AAAA,EACX,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG;AAAA,IACX,SAAS,MAAM,QAAQ,KAAK,GAAG;AAAA,EACjC,CAAC;AACD,QAAM,cAAc,WAAW,MAAM,MAAM;AAC3C,MAAI,YAAY,SAAS,GAAG;AAC1B,eAAW,OAAO,IAAI;AAAA,EACxB;AACA,QAAM,MAAM,CAAC;AACb,MAAI,KAAK;AAAA,IACP,KAAK;AAAA,IACL;AAAA,IACA,UAAU,CAAC,OAAO;AAAA,EACpB,CAAC;AACD,MAAI,OAAO;AACT,QAAI,KAAK;AAAA,MACP,KAAK;AAAA,MACL,YAAY;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU,CAAC,KAAK;AAAA,IAClB,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEA,IAAM;AAAA,EACJ,QAAQ;AACV,IAAI;AACJ,SAAS,YAAYI,OAAM;AACzB,QAAM,QAAQA,MAAK,CAAC;AACpB,QAAM,SAASA,MAAK,CAAC;AACrB,QAAM,CAAC,UAAU,IAAIA,MAAK,MAAM,CAAC;AACjC,MAAI,UAAU;AACd,MAAI,MAAM,QAAQ,UAAU,GAAG;AAC7B,cAAU;AAAA,MACR,KAAK;AAAA,MACL,YAAY;AAAA,QACV,OAAO,GAAG,OAAO,OAAO,WAAW,GAAG,EAAE,OAAO,gBAAgB,KAAK;AAAA,MACtE;AAAA,MACA,UAAU,CAAC;AAAA,QACT,KAAK;AAAA,QACL,YAAY;AAAA,UACV,OAAO,GAAG,OAAO,OAAO,WAAW,GAAG,EAAE,OAAO,gBAAgB,SAAS;AAAA,UACxE,MAAM;AAAA,UACN,GAAG,WAAW,CAAC;AAAA,QACjB;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,YAAY;AAAA,UACV,OAAO,GAAG,OAAO,OAAO,WAAW,GAAG,EAAE,OAAO,gBAAgB,OAAO;AAAA,UACtE,MAAM;AAAA,UACN,GAAG,WAAW,CAAC;AAAA,QACjB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,OAAO;AACL,cAAU;AAAA,MACR,KAAK;AAAA,MACL,YAAY;AAAA,QACV,MAAM;AAAA,QACN,GAAG;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR;AACF;AACA,IAAM,6BAA6B;AAAA,EACjC,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AACV;AACA,SAAS,mBAAmB,UAAU,QAAQ;AAC5C,MAAI,CAAC,cAAc,CAAC,OAAO,oBAAoB,UAAU;AACvD,YAAQ,MAAM,mBAAoB,OAAO,UAAU,gBAAkB,EAAE,OAAO,QAAQ,eAAgB,CAAC;AAAA,EACzG;AACF;AACA,SAAS,SAAS,UAAU,QAAQ;AAClC,MAAI,cAAc;AAClB,MAAI,WAAW,QAAQ,OAAO,iBAAiB,MAAM;AACnD,aAAS,uBAAuB;AAAA,EAClC;AACA,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI,gBAAgB,MAAM;AACxB,YAAM,OAAO,UAAU,QAAQ,KAAK,CAAC;AACrC,iBAAW,KAAK,YAAY;AAC5B,eAAS,KAAK,UAAU;AAAA,IAC1B;AACA,QAAI,YAAY,UAAU,SAAS,MAAM,KAAK,SAAS,MAAM,EAAE,QAAQ,GAAG;AACxE,YAAMA,QAAO,SAAS,MAAM,EAAE,QAAQ;AACtC,aAAO,QAAQ,YAAYA,KAAI,CAAC;AAAA,IAClC;AACA,uBAAmB,UAAU,MAAM;AACnC,YAAQ,eAAe,eAAe,CAAC,GAAG,0BAA0B,GAAG,CAAC,GAAG;AAAA,MACzE,MAAM,OAAO,oBAAoB,WAAW,aAAa,qBAAqB,KAAK,CAAC,IAAI,CAAC;AAAA,IAC3F,CAAC,CAAC;AAAA,EACJ,CAAC;AACH;AAEA,IAAM,SAAS,MAAM;AAAC;AACtB,IAAM,MAAM,OAAO,sBAAsB,eAAe,YAAY,QAAQ,YAAY,UAAU,cAAc;AAAA,EAC9G,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,WAAW;AACjB,IAAM,QAAQ,UAAQ;AACpB,MAAI,KAAK,GAAG,OAAO,UAAU,GAAG,EAAE,OAAO,MAAM,SAAS,CAAC;AACzD,SAAO,MAAM,IAAI,IAAI;AACvB;AACA,IAAM,MAAM,UAAQ;AAClB,MAAI,KAAK,GAAG,OAAO,UAAU,GAAG,EAAE,OAAO,MAAM,OAAO,CAAC;AACvD,MAAI,QAAQ,GAAG,OAAO,UAAU,GAAG,EAAE,OAAO,IAAI,GAAG,GAAG,OAAO,UAAU,GAAG,EAAE,OAAO,MAAM,SAAS,GAAG,GAAG,OAAO,UAAU,GAAG,EAAE,OAAO,MAAM,OAAO,CAAC;AACrJ;AACA,IAAI,OAAO;AAAA,EACT;AAAA,EACA;AACF;AAEA,IAAM,SAAS,MAAM;AAAC;AACtB,SAAS,UAAU,MAAM;AACvB,QAAM,QAAQ,KAAK,eAAe,KAAK,aAAa,aAAa,IAAI;AACrE,SAAO,OAAO,UAAU;AAC1B;AACA,SAAS,iBAAiB,MAAM;AAC9B,QAAM,SAAS,KAAK,eAAe,KAAK,aAAa,WAAW,IAAI;AACpE,QAAMA,QAAO,KAAK,eAAe,KAAK,aAAa,SAAS,IAAI;AAChE,SAAO,UAAUA;AACnB;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,QAAQ,KAAK,aAAa,KAAK,UAAU,YAAY,KAAK,UAAU,SAAS,OAAO,gBAAgB;AAC7G;AACA,SAAS,aAAa;AACpB,MAAI,OAAO,mBAAmB,MAAM;AAClC,WAAO,SAAS;AAAA,EAClB;AACA,QAAM,UAAU,SAAS,OAAO,cAAc;AAC9C,SAAO,WAAW,SAAS;AAC7B;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,SAAS,gBAAgB,8BAA8B,GAAG;AACnE;AACA,SAAS,cAAc,KAAK;AAC1B,SAAO,SAAS,cAAc,GAAG;AACnC;AACA,SAAS,WAAW,aAAa;AAC/B,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,QAAM;AAAA,IACJ,OAAO,YAAY,QAAQ,QAAQ,kBAAkB;AAAA,EACvD,IAAI;AACJ,MAAI,OAAO,gBAAgB,UAAU;AACnC,WAAO,SAAS,eAAe,WAAW;AAAA,EAC5C;AACA,QAAM,MAAM,KAAK,YAAY,GAAG;AAChC,SAAO,KAAK,YAAY,cAAc,CAAC,CAAC,EAAE,QAAQ,SAAU,KAAK;AAC/D,QAAI,aAAa,KAAK,YAAY,WAAW,GAAG,CAAC;AAAA,EACnD,CAAC;AACD,QAAM,WAAW,YAAY,YAAY,CAAC;AAC1C,WAAS,QAAQ,SAAU,OAAO;AAChC,QAAI,YAAY,WAAW,OAAO;AAAA,MAChC;AAAA,IACF,CAAC,CAAC;AAAA,EACJ,CAAC;AACD,SAAO;AACT;AACA,SAAS,cAAc,MAAM;AAC3B,MAAI,UAAU,IAAI,OAAO,KAAK,WAAW,GAAG;AAE5C,YAAU,GAAG,OAAO,SAAS,+BAA+B;AAE5D,SAAO;AACT;AACA,IAAM,WAAW;AAAA,EACf,SAAS,SAAU,UAAU;AAC3B,UAAM,OAAO,SAAS,CAAC;AACvB,QAAI,KAAK,YAAY;AACnB,eAAS,CAAC,EAAE,QAAQ,cAAY;AAC9B,aAAK,WAAW,aAAa,WAAW,QAAQ,GAAG,IAAI;AAAA,MACzD,CAAC;AACD,UAAI,KAAK,aAAa,aAAa,MAAM,QAAQ,OAAO,oBAAoB;AAC1E,YAAI,UAAU,SAAS,cAAc,cAAc,IAAI,CAAC;AACxD,aAAK,WAAW,aAAa,SAAS,IAAI;AAAA,MAC5C,OAAO;AACL,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM,SAAU,UAAU;AACxB,UAAM,OAAO,SAAS,CAAC;AACvB,UAAM,WAAW,SAAS,CAAC;AAI3B,QAAI,CAAC,WAAW,IAAI,EAAE,QAAQ,OAAO,gBAAgB,GAAG;AACtD,aAAO,SAAS,QAAQ,QAAQ;AAAA,IAClC;AACA,UAAM,SAAS,IAAI,OAAO,GAAG,OAAO,OAAO,WAAW,KAAK,CAAC;AAC5D,WAAO,SAAS,CAAC,EAAE,WAAW;AAC9B,QAAI,SAAS,CAAC,EAAE,WAAW,OAAO;AAChC,YAAM,eAAe,SAAS,CAAC,EAAE,WAAW,MAAM,MAAM,GAAG,EAAE,OAAO,CAAC,KAAK,QAAQ;AAChF,YAAI,QAAQ,OAAO,oBAAoB,IAAI,MAAM,MAAM,GAAG;AACxD,cAAI,MAAM,KAAK,GAAG;AAAA,QACpB,OAAO;AACL,cAAI,OAAO,KAAK,GAAG;AAAA,QACrB;AACA,eAAO;AAAA,MACT,GAAG;AAAA,QACD,QAAQ,CAAC;AAAA,QACT,OAAO,CAAC;AAAA,MACV,CAAC;AACD,eAAS,CAAC,EAAE,WAAW,QAAQ,aAAa,MAAM,KAAK,GAAG;AAC1D,UAAI,aAAa,OAAO,WAAW,GAAG;AACpC,aAAK,gBAAgB,OAAO;AAAA,MAC9B,OAAO;AACL,aAAK,aAAa,SAAS,aAAa,OAAO,KAAK,GAAG,CAAC;AAAA,MAC1D;AAAA,IACF;AACA,UAAM,eAAe,SAAS,IAAI,OAAK,OAAO,CAAC,CAAC,EAAE,KAAK,IAAI;AAC3D,SAAK,aAAa,eAAe,EAAE;AACnC,SAAK,YAAY;AAAA,EACnB;AACF;AACA,SAAS,qBAAqB,IAAI;AAChC,KAAG;AACL;AACA,SAAS,QAAQ,WAAW,UAAU;AACpC,QAAM,mBAAmB,OAAO,aAAa,aAAa,WAAW;AACrE,MAAI,UAAU,WAAW,GAAG;AAC1B,qBAAiB;AAAA,EACnB,OAAO;AACL,QAAI,QAAQ;AACZ,QAAI,OAAO,mBAAmB,yBAAyB;AACrD,cAAQ,OAAO,yBAAyB;AAAA,IAC1C;AACA,UAAM,MAAM;AACV,YAAM,UAAU,WAAW;AAC3B,YAAM,OAAO,KAAK,MAAM,QAAQ;AAChC,gBAAU,IAAI,OAAO;AACrB,WAAK;AACL,uBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,IAAI,WAAW;AACf,SAAS,qBAAqB;AAC5B,aAAW;AACb;AACA,SAAS,oBAAoB;AAC3B,aAAW;AACb;AACA,IAAI,KAAK;AACT,SAAS,QAAQ,SAAS;AACxB,MAAI,CAAC,mBAAmB;AACtB;AAAA,EACF;AACA,MAAI,CAAC,OAAO,kBAAkB;AAC5B;AAAA,EACF;AACA,QAAM;AAAA,IACJ,eAAe;AAAA,IACf,eAAe;AAAA,IACf,yBAAyB;AAAA,IACzB,uBAAuB;AAAA,EACzB,IAAI;AACJ,OAAK,IAAI,kBAAkB,aAAW;AACpC,QAAI,SAAU;AACd,UAAM,gBAAgB,uBAAuB;AAC7C,YAAQ,OAAO,EAAE,QAAQ,oBAAkB;AACzC,UAAI,eAAe,SAAS,eAAe,eAAe,WAAW,SAAS,KAAK,CAAC,UAAU,eAAe,WAAW,CAAC,CAAC,GAAG;AAC3H,YAAI,OAAO,sBAAsB;AAC/B,iCAAuB,eAAe,MAAM;AAAA,QAC9C;AACA,qBAAa,eAAe,MAAM;AAAA,MACpC;AACA,UAAI,eAAe,SAAS,gBAAgB,eAAe,OAAO,cAAc,OAAO,sBAAsB;AAC3G,+BAAuB,eAAe,OAAO,UAAU;AAAA,MACzD;AACA,UAAI,eAAe,SAAS,gBAAgB,UAAU,eAAe,MAAM,KAAK,CAAC,gCAAgC,QAAQ,eAAe,aAAa,GAAG;AACtJ,YAAI,eAAe,kBAAkB,WAAW,iBAAiB,eAAe,MAAM,GAAG;AACvF,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,iBAAiB,WAAW,eAAe,MAAM,CAAC;AACtD,yBAAe,OAAO,aAAa,aAAa,UAAU,aAAa;AACvE,cAAI,SAAU,gBAAe,OAAO,aAAa,WAAW,QAAQ;AAAA,QACtE,WAAW,gBAAgB,eAAe,MAAM,GAAG;AACjD,uBAAa,eAAe,MAAM;AAAA,QACpC;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,MAAI,CAAC,OAAQ;AACb,KAAG,QAAQ,sBAAsB;AAAA,IAC/B,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,SAAS;AAAA,EACX,CAAC;AACH;AACA,SAAS,aAAa;AACpB,MAAI,CAAC,GAAI;AACT,KAAG,WAAW;AAChB;AAEA,SAAS,YAAa,MAAM;AAC1B,QAAM,QAAQ,KAAK,aAAa,OAAO;AACvC,MAAI,MAAM,CAAC;AACX,MAAI,OAAO;AACT,UAAM,MAAM,MAAM,GAAG,EAAE,OAAO,CAAC,KAAKE,WAAU;AAC5C,YAAMN,UAASM,OAAM,MAAM,GAAG;AAC9B,YAAM,OAAON,QAAO,CAAC;AACrB,YAAM,QAAQA,QAAO,MAAM,CAAC;AAC5B,UAAI,QAAQ,MAAM,SAAS,GAAG;AAC5B,YAAI,IAAI,IAAI,MAAM,KAAK,GAAG,EAAE,KAAK;AAAA,MACnC;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AACA,SAAO;AACT;AAEA,SAAS,YAAa,MAAM;AAC1B,QAAM,iBAAiB,KAAK,aAAa,aAAa;AACtD,QAAM,mBAAmB,KAAK,aAAa,WAAW;AACtD,QAAM,YAAY,KAAK,cAAc,SAAY,KAAK,UAAU,KAAK,IAAI;AACzE,MAAI,MAAM,iBAAiB,WAAW,IAAI,CAAC;AAC3C,MAAI,CAAC,IAAI,QAAQ;AACf,QAAI,SAAS,uBAAuB;AAAA,EACtC;AACA,MAAI,kBAAkB,kBAAkB;AACtC,QAAI,SAAS;AACb,QAAI,WAAW;AAAA,EACjB;AACA,MAAI,IAAI,YAAY,IAAI,QAAQ;AAC9B,WAAO;AAAA,EACT;AACA,MAAI,IAAI,UAAU,UAAU,SAAS,GAAG;AACtC,QAAI,WAAW,WAAW,IAAI,QAAQ,KAAK,SAAS,KAAK,UAAU,IAAI,QAAQ,MAAM,KAAK,SAAS,CAAC;AAAA,EACtG;AACA,MAAI,CAAC,IAAI,YAAY,OAAO,gBAAgB,KAAK,cAAc,KAAK,WAAW,aAAa,KAAK,WAAW;AAC1G,QAAI,WAAW,KAAK,WAAW;AAAA,EACjC;AACA,SAAO;AACT;AAEA,SAAS,iBAAkB,MAAM;AAC/B,QAAM,kBAAkB,QAAQ,KAAK,UAAU,EAAE,OAAO,CAAC,KAAK,SAAS;AACrE,QAAI,IAAI,SAAS,WAAW,IAAI,SAAS,SAAS;AAChD,UAAI,KAAK,IAAI,IAAI,KAAK;AAAA,IACxB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,QAAM,QAAQ,KAAK,aAAa,OAAO;AACvC,QAAM,UAAU,KAAK,aAAa,kBAAkB;AACpD,MAAI,OAAO,UAAU;AACnB,QAAI,OAAO;AACT,sBAAgB,iBAAiB,IAAI,GAAG,OAAO,OAAO,kBAAkB,SAAS,EAAE,OAAO,WAAW,aAAa,CAAC;AAAA,IACrH,OAAO;AACL,sBAAgB,aAAa,IAAI;AACjC,sBAAgB,WAAW,IAAI;AAAA,IACjC;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,YAAY;AACnB,SAAO;AAAA,IACL,UAAU;AAAA,IACV,OAAO;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,MAAM;AAAA,MACJ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,MAAM,CAAC;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,IACR,OAAO;AAAA,MACL,SAAS,CAAC;AAAA,MACV,QAAQ,CAAC;AAAA,MACT,YAAY,CAAC;AAAA,IACf;AAAA,EACF;AACF;AACA,SAAS,UAAU,MAAM;AACvB,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAAA,IAC/E,aAAa;AAAA,EACf;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR,IAAI,YAAY,IAAI;AACpB,QAAM,kBAAkB,iBAAiB,IAAI;AAC7C,QAAM,aAAa,WAAW,uBAAuB,CAAC,GAAG,IAAI;AAC7D,MAAI,cAAc,OAAO,cAAc,YAAY,IAAI,IAAI,CAAC;AAC5D,SAAO,eAAe;AAAA,IACpB;AAAA,IACA,OAAO,KAAK,aAAa,OAAO;AAAA,IAChC,SAAS,KAAK,aAAa,kBAAkB;AAAA,IAC7C;AAAA,IACA,WAAW;AAAA,IACX,MAAM;AAAA,MACJ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,MAAM,CAAC;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,YAAY;AAAA,IACd;AAAA,EACF,GAAG,UAAU;AACf;AAEA,IAAM;AAAA,EACJ,QAAQ;AACV,IAAI;AACJ,SAAS,iBAAiB,MAAM;AAC9B,QAAM,WAAW,OAAO,mBAAmB,SAAS,UAAU,MAAM;AAAA,IAClE,aAAa;AAAA,EACf,CAAC,IAAI,UAAU,IAAI;AACnB,MAAI,CAAC,SAAS,MAAM,QAAQ,QAAQ,qBAAqB,GAAG;AAC1D,WAAO,aAAa,sBAAsB,MAAM,QAAQ;AAAA,EAC1D,OAAO;AACL,WAAO,aAAa,kCAAkC,MAAM,QAAQ;AAAA,EACtE;AACF;AACA,SAAS,mBAAmB;AAC1B,SAAO,CAAC,GAAG,IAAI,GAAG,EAAE;AACtB;AACA,SAAS,OAAO,MAAM;AACpB,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACnF,MAAI,CAAC,OAAQ,QAAO,QAAQ,QAAQ;AACpC,QAAM,gBAAgB,SAAS,gBAAgB;AAC/C,QAAM,SAAS,YAAU,cAAc,IAAI,GAAG,OAAO,6BAA6B,GAAG,EAAE,OAAO,MAAM,CAAC;AACrG,QAAM,YAAY,YAAU,cAAc,OAAO,GAAG,OAAO,6BAA6B,GAAG,EAAE,OAAO,MAAM,CAAC;AAC3G,QAAM,WAAW,OAAO,eAAe,iBAAiB,IAAI,EAAE,OAAO,OAAO,KAAK,QAAQ,CAAC;AAC1F,MAAI,CAAC,SAAS,SAAS,IAAI,GAAG;AAC5B,aAAS,KAAK,IAAI;AAAA,EACpB;AACA,QAAM,mBAAmB,CAAC,IAAI,OAAO,uBAAuB,QAAQ,EAAE,OAAO,eAAe,IAAI,CAAC,EAAE,OAAO,SAAS,IAAI,UAAQ,IAAI,OAAO,MAAM,QAAQ,EAAE,OAAO,eAAe,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI;AACjM,MAAI,iBAAiB,WAAW,GAAG;AACjC,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,MAAI,aAAa,CAAC;AAClB,MAAI;AACF,iBAAa,QAAQ,KAAK,iBAAiB,gBAAgB,CAAC;AAAA,EAC9D,SAAS,MAAM;AAAA,EAEf;AACA,MAAI,WAAW,SAAS,GAAG;AACzB,WAAO,SAAS;AAChB,cAAU,UAAU;AAAA,EACtB,OAAO;AACL,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,QAAM,OAAO,KAAK,MAAM,QAAQ;AAChC,QAAM,YAAY,WAAW,OAAO,CAAC,KAAK,SAAS;AACjD,QAAI;AACF,YAAM,WAAW,iBAAiB,IAAI;AACtC,UAAI,UAAU;AACZ,YAAI,KAAK,QAAQ;AAAA,MACnB;AAAA,IACF,SAAS,MAAM;AACb,UAAI,CAAC,YAAY;AACf,YAAI,KAAK,SAAS,eAAe;AAC/B,kBAAQ,MAAM,IAAI;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAQ,IAAI,SAAS,EAAE,KAAK,uBAAqB;AAC/C,cAAQ,mBAAmB,MAAM;AAC/B,eAAO,QAAQ;AACf,eAAO,UAAU;AACjB,kBAAU,SAAS;AACnB,YAAI,OAAO,aAAa,WAAY,UAAS;AAC7C,aAAK;AACL,gBAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC,EAAE,MAAM,UAAQ;AACf,WAAK;AACL,aAAO,IAAI;AAAA,IACb,CAAC;AAAA,EACH,CAAC;AACH;AACA,SAAS,OAAO,MAAM;AACpB,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACnF,mBAAiB,IAAI,EAAE,KAAK,cAAY;AACtC,QAAI,UAAU;AACZ,cAAQ,CAAC,QAAQ,GAAG,QAAQ;AAAA,IAC9B;AAAA,EACF,CAAC;AACH;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO,SAAU,qBAAqB;AACpC,QAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,UAAM,kBAAkB,uBAAuB,CAAC,GAAG,OAAO,sBAAsB,mBAAmB,uBAAuB,CAAC,CAAC;AAC5H,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,MAAM;AACR,cAAQ,QAAQ,CAAC,GAAG,OAAO,OAAO,mBAAmB,QAAQ,CAAC,CAAC;AAAA,IACjE;AACA,WAAO,KAAK,gBAAgB,eAAe,eAAe,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG;AAAA,MACzE;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AACF;AACA,IAAM,SAAS,SAAU,gBAAgB;AACvC,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,OAAO;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU,CAAC;AAAA,IACX,aAAa,CAAC;AAAA,IACd,QAAAA,UAAS,CAAC;AAAA,EACZ,IAAI;AACJ,MAAI,CAAC,eAAgB;AACrB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,MAAAI;AAAA,EACF,IAAI;AACJ,SAAO,YAAY,eAAe;AAAA,IAChC,MAAM;AAAA,EACR,GAAG,cAAc,GAAG,MAAM;AACxB,cAAU,4BAA4B;AAAA,MACpC;AAAA,MACA;AAAA,IACF,CAAC;AACD,QAAI,OAAO,UAAU;AACnB,UAAI,OAAO;AACT,mBAAW,iBAAiB,IAAI,GAAG,OAAO,OAAO,kBAAkB,SAAS,EAAE,OAAO,WAAW,aAAa,CAAC;AAAA,MAChH,OAAO;AACL,mBAAW,aAAa,IAAI;AAC5B,mBAAW,WAAW,IAAI;AAAA,MAC5B;AAAA,IACF;AACA,WAAO,sBAAsB;AAAA,MAC3B,OAAO;AAAA,QACL,MAAM,YAAYA,KAAI;AAAA,QACtB,MAAM,OAAO,YAAY,KAAK,IAAI,IAAI;AAAA,UACpC,OAAO;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,MAAM,CAAC;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW,eAAe,eAAe,CAAC,GAAG,oBAAoB,GAAG,SAAS;AAAA,MAC7E;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO;AAAA,QACL;AAAA,QACA,QAAAJ;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAI,kBAAkB;AAAA,EACpB,SAAS;AACP,WAAO;AAAA,MACL,MAAM,aAAa,MAAM;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,QAAQ;AACN,WAAO;AAAA,MACL,0BAA0B,aAAa;AACrC,oBAAY,eAAe;AAC3B,oBAAY,eAAe;AAC3B,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,cAAc;AACrB,iBAAa,QAAQ,SAAU,QAAQ;AACrC,YAAM;AAAA,QACJ,OAAO;AAAA,QACP,WAAW,MAAM;AAAA,QAAC;AAAA,MACpB,IAAI;AACJ,aAAO,OAAO,MAAM,QAAQ;AAAA,IAC9B;AACA,iBAAa,iCAAiC,SAAU,MAAM,UAAU;AACtE,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,gBAAQ,IAAI,CAAC,SAAS,UAAU,MAAM,GAAG,KAAK,WAAW,SAAS,KAAK,UAAU,KAAK,MAAM,IAAI,QAAQ,QAAQ;AAAA,UAC9G,OAAO;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,MAAM,CAAC;AAAA,QACT,CAAC,CAAC,CAAC,EAAE,KAAK,UAAQ;AAChB,cAAI,CAAC,MAAMO,KAAI,IAAI;AACnB,kBAAQ,CAAC,MAAM,sBAAsB;AAAA,YACnC,OAAO;AAAA,cACL;AAAA,cACA,MAAAA;AAAA,YACF;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,WAAW;AAAA,UACb,CAAC,CAAC,CAAC;AAAA,QACL,CAAC,EAAE,MAAM,MAAM;AAAA,MACjB,CAAC;AAAA,IACH;AACA,iBAAa,uBAAuB,SAAU,OAAO;AACnD,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAAP;AAAA,MACF,IAAI;AACJ,YAAM,cAAc,WAAWA,OAAM;AACrC,UAAI,YAAY,SAAS,GAAG;AAC1B,mBAAW,OAAO,IAAI;AAAA,MACxB;AACA,UAAI;AACJ,UAAI,sBAAsB,SAAS,GAAG;AACpC,oBAAY,aAAa,qCAAqC;AAAA,UAC5D;AAAA,UACA;AAAA,UACA,gBAAgB,KAAK;AAAA,UACrB,WAAW,KAAK;AAAA,QAClB,CAAC;AAAA,MACH;AACA,eAAS,KAAK,aAAa,KAAK,IAAI;AACpC,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,SAAS;AAAA,EACX,SAAS;AACP,WAAO;AAAA,MACL,MAAM,WAAW;AACf,YAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,cAAM;AAAA,UACJ,UAAU,CAAC;AAAA,QACb,IAAI;AACJ,eAAO,YAAY;AAAA,UACjB,MAAM;AAAA,QACR,GAAG,MAAM;AACP,oBAAU,4BAA4B;AAAA,YACpC;AAAA,YACA;AAAA,UACF,CAAC;AACD,cAAI,WAAW,CAAC;AAChB,oBAAU,UAAQ;AAChB,kBAAM,QAAQ,IAAI,IAAI,KAAK,IAAI,OAAK;AAClC,yBAAW,SAAS,OAAO,EAAE,QAAQ;AAAA,YACvC,CAAC,IAAI,WAAW,SAAS,OAAO,KAAK,QAAQ;AAAA,UAC/C,CAAC;AACD,iBAAO,CAAC;AAAA,YACN,KAAK;AAAA,YACL,YAAY;AAAA,cACV,OAAO,CAAC,GAAG,OAAO,OAAO,WAAW,SAAS,GAAG,GAAG,OAAO,EAAE,KAAK,GAAG;AAAA,YACtE;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,gBAAgB;AAAA,EAClB,SAAS;AACP,WAAO;AAAA,MACL,QAAQ,SAAS;AACf,YAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,cAAM;AAAA,UACJ,QAAQ;AAAA,UACR,UAAU,CAAC;AAAA,UACX,aAAa,CAAC;AAAA,UACd,QAAAA,UAAS,CAAC;AAAA,QACZ,IAAI;AACJ,eAAO,YAAY;AAAA,UACjB,MAAM;AAAA,UACN;AAAA,QACF,GAAG,MAAM;AACP,oBAAU,4BAA4B;AAAA,YACpC;AAAA,YACA;AAAA,UACF,CAAC;AACD,iBAAO,0BAA0B;AAAA,YAC/B,SAAS,QAAQ,SAAS;AAAA,YAC1B;AAAA,YACA,OAAO;AAAA,cACL;AAAA,cACA,QAAAA;AAAA,cACA,SAAS,CAAC,GAAG,OAAO,OAAO,WAAW,iBAAiB,GAAG,GAAG,OAAO;AAAA,YACtE;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,aAAa;AAAA,EACf,SAAS;AACP,WAAO;AAAA,MACL,KAAK,SAAS;AACZ,YAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,cAAM;AAAA,UACJ,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,UAAU,CAAC;AAAA,UACX,aAAa,CAAC;AAAA,UACd,QAAAA,UAAS,CAAC;AAAA,QACZ,IAAI;AACJ,eAAO,YAAY;AAAA,UACjB,MAAM;AAAA,UACN;AAAA,QACF,GAAG,MAAM;AACP,oBAAU,4BAA4B;AAAA,YACpC;AAAA,YACA;AAAA,UACF,CAAC;AACD,iBAAO,uBAAuB;AAAA,YAC5B;AAAA,YACA,WAAW,eAAe,eAAe,CAAC,GAAG,oBAAoB,GAAG,SAAS;AAAA,YAC7E;AAAA,YACA,OAAO;AAAA,cACL;AAAA,cACA,QAAAA;AAAA,cACA,SAAS,CAAC,GAAG,OAAO,OAAO,WAAW,cAAc,GAAG,GAAG,OAAO;AAAA,YACnE;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,cAAc;AACrB,iBAAa,qBAAqB,SAAU,MAAM,UAAU;AAC1D,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,QAAQ;AACZ,UAAI,SAAS;AACb,UAAI,OAAO;AACT,cAAM,mBAAmB,SAAS,iBAAiB,IAAI,EAAE,UAAU,EAAE;AACrE,cAAM,qBAAqB,KAAK,sBAAsB;AACtD,gBAAQ,mBAAmB,QAAQ;AACnC,iBAAS,mBAAmB,SAAS;AAAA,MACvC;AACA,UAAI,OAAO,YAAY,CAAC,OAAO;AAC7B,cAAM,WAAW,aAAa,IAAI;AAAA,MACpC;AACA,aAAO,QAAQ,QAAQ,CAAC,MAAM,uBAAuB;AAAA,QACnD,SAAS,KAAK;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW;AAAA,MACb,CAAC,CAAC,CAAC;AAAA,IACL;AAAA,EACF;AACF;AAEA,IAAM,wBAAwB,IAAI,OAAO,KAAU,IAAI;AACvD,IAAM,0BAA0B,CAAC,SAAS,OAAO;AACjD,IAAM,gCAAgC,eAAe,eAAe,eAAe,eAAe,CAAC,GAAG;AAAA,EACpG,aAAa;AAAA,IACX,QAAQ;AAAA,IACR,KAAK;AAAA,EACP;AACF,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChB,IAAM,+BAA+B,OAAO,KAAK,6BAA6B,EAAE,OAAO,CAAC,KAAK,QAAQ;AACnG,MAAI,IAAI,YAAY,CAAC,IAAI,8BAA8B,GAAG;AAC1D,SAAO;AACT,GAAG,CAAC,CAAC;AACL,IAAM,8BAA8B,OAAO,KAAK,4BAA4B,EAAE,OAAO,CAAC,KAAK,eAAe;AACxG,QAAM,UAAU,6BAA6B,UAAU;AACvD,MAAI,UAAU,IAAI,QAAQ,GAAG,KAAK,CAAC,GAAG,OAAO,QAAQ,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;AACnE,SAAO;AACT,GAAG,CAAC,CAAC;AACL,SAAS,oBAAoB,SAAS;AACpC,QAAM,UAAU,QAAQ,QAAQ,uBAAuB,EAAE;AACzD,QAAM,YAAY,YAAY,SAAS,CAAC;AACxC,QAAM,eAAe,aAAa,wBAAwB,CAAC,KAAK,aAAa,wBAAwB,CAAC;AACtG,QAAM,YAAY,QAAQ,WAAW,IAAI,QAAQ,CAAC,MAAM,QAAQ,CAAC,IAAI;AACrE,SAAO;AAAA,IACL,OAAO,YAAY,MAAM,QAAQ,CAAC,CAAC,IAAI,MAAM,OAAO;AAAA,IACpD,aAAa,gBAAgB;AAAA,EAC/B;AACF;AACA,SAAS,UAAU,YAAY,YAAY;AACzC,QAAM,sBAAsB,WAAW,QAAQ,gBAAgB,EAAE,EAAE,YAAY;AAC/E,QAAM,oBAAoB,SAAS,UAAU;AAC7C,QAAM,sBAAsB,MAAM,iBAAiB,IAAI,WAAW;AAClE,UAAQ,6BAA6B,mBAAmB,KAAK,CAAC,GAAG,mBAAmB,KAAK,4BAA4B,mBAAmB;AAC1I;AACA,SAAS,mBAAmB,MAAM,UAAU;AAC1C,QAAM,mBAAmB,GAAG,OAAO,8BAA8B,EAAE,OAAO,SAAS,QAAQ,KAAK,GAAG,CAAC;AACpG,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI,KAAK,aAAa,gBAAgB,MAAM,MAAM;AAEhD,aAAO,QAAQ;AAAA,IACjB;AACA,UAAM,WAAW,QAAQ,KAAK,QAAQ;AACtC,UAAM,gCAAgC,SAAS,OAAO,UAAQ,KAAK,aAAa,sBAAsB,MAAM,QAAQ,EAAE,CAAC;AACvH,UAAMA,UAAS,OAAO,iBAAiB,MAAM,QAAQ;AACrD,UAAM,aAAaA,QAAO,iBAAiB,aAAa;AACxD,UAAM,kBAAkB,WAAW,MAAM,mBAAmB;AAC5D,UAAM,aAAaA,QAAO,iBAAiB,aAAa;AACxD,UAAM,UAAUA,QAAO,iBAAiB,SAAS;AACjD,QAAI,iCAAiC,CAAC,iBAAiB;AAIrD,WAAK,YAAY,6BAA6B;AAC9C,aAAO,QAAQ;AAAA,IACjB,WAAW,mBAAmB,YAAY,UAAU,YAAY,IAAI;AAClE,YAAMQ,WAAUR,QAAO,iBAAiB,SAAS;AACjD,UAAI,SAAS,UAAU,YAAY,UAAU;AAC7C,YAAM;AAAA,QACJ,OAAO;AAAA,QACP;AAAA,MACF,IAAI,oBAAoBQ,QAAO;AAC/B,YAAM,OAAO,gBAAgB,CAAC,EAAE,WAAW,aAAa;AACxD,UAAI,WAAW,UAAU,QAAQ,QAAQ;AACzC,UAAI,iBAAiB;AACrB,UAAI,MAAM;AACR,cAAM,YAAY,aAAa,QAAQ;AACvC,YAAI,UAAU,YAAY,UAAU,QAAQ;AAC1C,qBAAW,UAAU;AACrB,mBAAS,UAAU;AAAA,QACrB;AAAA,MACF;AAIA,UAAI,YAAY,CAAC,gBAAgB,CAAC,iCAAiC,8BAA8B,aAAa,WAAW,MAAM,UAAU,8BAA8B,aAAa,SAAS,MAAM,iBAAiB;AAClN,aAAK,aAAa,kBAAkB,cAAc;AAClD,YAAI,+BAA+B;AAEjC,eAAK,YAAY,6BAA6B;AAAA,QAChD;AACA,cAAM,OAAO,UAAU;AACvB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,cAAM,WAAW,sBAAsB,IAAI;AAC3C,iBAAS,UAAU,MAAM,EAAE,KAAK,UAAQ;AACtC,gBAAM,WAAW,sBAAsB,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,YAClF,OAAO;AAAA,cACL;AAAA,cACA,MAAM,mBAAmB;AAAA,YAC3B;AAAA,YACA;AAAA,YACA,UAAU;AAAA,YACV;AAAA,YACA,WAAW;AAAA,UACb,CAAC,CAAC;AACF,gBAAM,UAAU,SAAS,gBAAgB,8BAA8B,KAAK;AAC5E,cAAI,aAAa,YAAY;AAC3B,iBAAK,aAAa,SAAS,KAAK,UAAU;AAAA,UAC5C,OAAO;AACL,iBAAK,YAAY,OAAO;AAAA,UAC1B;AACA,kBAAQ,YAAY,SAAS,IAAI,UAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,IAAI;AAChE,eAAK,gBAAgB,gBAAgB;AACrC,kBAAQ;AAAA,QACV,CAAC,EAAE,MAAM,MAAM;AAAA,MACjB,OAAO;AACL,gBAAQ;AAAA,MACV;AAAA,IACF,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACH;AACA,SAAS,QAAQ,MAAM;AACrB,SAAO,QAAQ,IAAI,CAAC,mBAAmB,MAAM,UAAU,GAAG,mBAAmB,MAAM,SAAS,CAAC,CAAC;AAChG;AACA,SAAS,YAAY,MAAM;AACzB,SAAO,KAAK,eAAe,SAAS,QAAQ,CAAC,CAAC,oCAAoC,QAAQ,KAAK,QAAQ,YAAY,CAAC,KAAK,CAAC,KAAK,aAAa,sBAAsB,MAAM,CAAC,KAAK,cAAc,KAAK,WAAW,YAAY;AAC1N;AACA,SAAS,qBAAqB,MAAM;AAClC,MAAI,CAAC,OAAQ;AACb,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,aAAa,QAAQ,KAAK,iBAAiB,GAAG,CAAC,EAAE,OAAO,WAAW,EAAE,IAAI,OAAO;AACtF,UAAMC,OAAM,KAAK,MAAM,sBAAsB;AAC7C,uBAAmB;AACnB,YAAQ,IAAI,UAAU,EAAE,KAAK,MAAM;AACjC,MAAAA,KAAI;AACJ,wBAAkB;AAClB,cAAQ;AAAA,IACV,CAAC,EAAE,MAAM,MAAM;AACb,MAAAA,KAAI;AACJ,wBAAkB;AAClB,aAAO;AAAA,IACT,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAI,iBAAiB;AAAA,EACnB,QAAQ;AACN,WAAO;AAAA,MACL,0BAA0B,aAAa;AACrC,oBAAY,yBAAyB;AACrC,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAASC,YAAW;AAClB,IAAAA,WAAU,qBAAqB,SAAU,QAAQ;AAC/C,YAAM;AAAA,QACJ,OAAO;AAAA,MACT,IAAI;AACJ,UAAI,OAAO,sBAAsB;AAC/B,6BAAqB,IAAI;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,aAAa;AACjB,IAAI,qBAAqB;AAAA,EACvB,SAAS;AACP,WAAO;AAAA,MACL,KAAK;AAAA,QACH,UAAU;AACR,6BAAmB;AACnB,uBAAa;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AACN,WAAO;AAAA,MACL,YAAY;AACV,gBAAQ,WAAW,6BAA6B,CAAC,CAAC,CAAC;AAAA,MACrD;AAAA,MACA,SAAS;AACP,mBAAW;AAAA,MACb;AAAA,MACA,MAAM,QAAQ;AACZ,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,YAAI,YAAY;AACd,4BAAkB;AAAA,QACpB,OAAO;AACL,kBAAQ,WAAW,6BAA6B;AAAA,YAC9C;AAAA,UACF,CAAC,CAAC;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,uBAAuB,qBAAmB;AAC9C,MAAI,YAAY;AAAA,IACd,MAAM;AAAA,IACN,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACA,SAAO,gBAAgB,YAAY,EAAE,MAAM,GAAG,EAAE,OAAO,CAAC,KAAK,MAAM;AACjE,UAAM,QAAQ,EAAE,YAAY,EAAE,MAAM,GAAG;AACvC,UAAM,QAAQ,MAAM,CAAC;AACrB,QAAI,OAAO,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AAClC,QAAI,SAAS,SAAS,KAAK;AACzB,UAAI,QAAQ;AACZ,aAAO;AAAA,IACT;AACA,QAAI,SAAS,SAAS,KAAK;AACzB,UAAI,QAAQ;AACZ,aAAO;AAAA,IACT;AACA,WAAO,WAAW,IAAI;AACtB,QAAI,MAAM,IAAI,GAAG;AACf,aAAO;AAAA,IACT;AACA,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,YAAI,OAAO,IAAI,OAAO;AACtB;AAAA,MACF,KAAK;AACH,YAAI,OAAO,IAAI,OAAO;AACtB;AAAA,MACF,KAAK;AACH,YAAI,IAAI,IAAI,IAAI;AAChB;AAAA,MACF,KAAK;AACH,YAAI,IAAI,IAAI,IAAI;AAChB;AAAA,MACF,KAAK;AACH,YAAI,IAAI,IAAI,IAAI;AAChB;AAAA,MACF,KAAK;AACH,YAAI,IAAI,IAAI,IAAI;AAChB;AAAA,MACF,KAAK;AACH,YAAI,SAAS,IAAI,SAAS;AAC1B;AAAA,IACJ;AACA,WAAO;AAAA,EACT,GAAG,SAAS;AACd;AACA,IAAI,kBAAkB;AAAA,EACpB,SAAS;AACP,WAAO;AAAA,MACL,OAAO;AAAA,QACL,WAAW,qBAAmB;AAC5B,iBAAO,qBAAqB,eAAe;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AACN,WAAO;AAAA,MACL,oBAAoB,aAAa,MAAM;AACrC,cAAM,kBAAkB,KAAK,aAAa,mBAAmB;AAC7D,YAAI,iBAAiB;AACnB,sBAAY,YAAY,qBAAqB,eAAe;AAAA,QAC9D;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAASA,YAAW;AAClB,IAAAA,WAAU,oCAAoC,SAAU,MAAM;AAC5D,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,QAAQ;AAAA,QACZ,WAAW,aAAa,OAAO,iBAAiB,GAAG,OAAO;AAAA,MAC5D;AACA,YAAM,iBAAiB,aAAa,OAAO,UAAU,IAAI,IAAI,IAAI,EAAE,OAAO,UAAU,IAAI,IAAI,IAAI;AAChG,YAAM,aAAa,SAAS,OAAO,UAAU,OAAO,MAAM,UAAU,QAAQ,KAAK,IAAI,IAAI,EAAE,OAAO,UAAU,OAAO,MAAM,UAAU,QAAQ,KAAK,IAAI,IAAI;AACxJ,YAAM,cAAc,UAAU,OAAO,UAAU,QAAQ,OAAO;AAC9D,YAAM,QAAQ;AAAA,QACZ,WAAW,GAAG,OAAO,gBAAgB,GAAG,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,WAAW;AAAA,MACtF;AACA,YAAM,OAAO;AAAA,QACX,WAAW,aAAa,OAAO,YAAY,IAAI,IAAI,QAAQ;AAAA,MAC7D;AACA,YAAM,aAAa;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO;AAAA,QACL,KAAK;AAAA,QACL,YAAY,eAAe,CAAC,GAAG,WAAW,KAAK;AAAA,QAC/C,UAAU,CAAC;AAAA,UACT,KAAK;AAAA,UACL,YAAY,eAAe,CAAC,GAAG,WAAW,KAAK;AAAA,UAC/C,UAAU,CAAC;AAAA,YACT,KAAK,KAAK,KAAK;AAAA,YACf,UAAU,KAAK,KAAK;AAAA,YACpB,YAAY,eAAe,eAAe,CAAC,GAAG,KAAK,KAAK,UAAU,GAAG,WAAW,IAAI;AAAA,UACtF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,YAAY;AAAA,EAChB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,OAAO;AAAA,EACP,QAAQ;AACV;AACA,SAAS,UAAU,UAAU;AAC3B,MAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAChF,MAAI,SAAS,eAAe,SAAS,WAAW,QAAQ,QAAQ;AAC9D,aAAS,WAAW,OAAO;AAAA,EAC7B;AACA,SAAO;AACT;AACA,SAAS,QAAQ,UAAU;AACzB,MAAI,SAAS,QAAQ,KAAK;AACxB,WAAO,SAAS;AAAA,EAClB,OAAO;AACL,WAAO,CAAC,QAAQ;AAAA,EAClB;AACF;AACA,IAAI,QAAQ;AAAA,EACV,QAAQ;AACN,WAAO;AAAA,MACL,oBAAoB,aAAa,MAAM;AACrC,cAAM,WAAW,KAAK,aAAa,cAAc;AACjD,cAAM,OAAO,CAAC,WAAW,mBAAmB,IAAI,iBAAiB,SAAS,MAAM,GAAG,EAAE,IAAI,OAAK,EAAE,KAAK,CAAC,CAAC;AACvG,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,SAAS,uBAAuB;AAAA,QACvC;AACA,oBAAY,OAAO;AACnB,oBAAY,SAAS,KAAK,aAAa,iBAAiB;AACxD,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAASA,YAAW;AAClB,IAAAA,WAAU,uBAAuB,SAAU,MAAM;AAC/C,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,QACR;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,IAAI;AACJ,YAAM;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,IAAI;AACJ,YAAM,QAAQ,gBAAgB;AAAA,QAC5B;AAAA,QACA,gBAAgB;AAAA,QAChB,WAAW;AAAA,MACb,CAAC;AACD,YAAM,WAAW;AAAA,QACf,KAAK;AAAA,QACL,YAAY,eAAe,eAAe,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG;AAAA,UAC5D,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AACA,YAAM,8BAA8B,SAAS,WAAW;AAAA,QACtD,UAAU,SAAS,SAAS,IAAI,SAAS;AAAA,MAC3C,IAAI,CAAC;AACL,YAAM,iBAAiB;AAAA,QACrB,KAAK;AAAA,QACL,YAAY,eAAe,CAAC,GAAG,MAAM,KAAK;AAAA,QAC1C,UAAU,CAAC,UAAU,eAAe;AAAA,UAClC,KAAK,SAAS;AAAA,UACd,YAAY,eAAe,eAAe,CAAC,GAAG,SAAS,UAAU,GAAG,MAAM,IAAI;AAAA,QAChF,GAAG,2BAA2B,CAAC,CAAC;AAAA,MAClC;AACA,YAAM,iBAAiB;AAAA,QACrB,KAAK;AAAA,QACL,YAAY,eAAe,CAAC,GAAG,MAAM,KAAK;AAAA,QAC1C,UAAU,CAAC,cAAc;AAAA,MAC3B;AACA,YAAM,SAAS,QAAQ,OAAO,kBAAkB,aAAa,CAAC;AAC9D,YAAM,SAAS,QAAQ,OAAO,kBAAkB,aAAa,CAAC;AAC9D,YAAM,UAAU;AAAA,QACd,KAAK;AAAA,QACL,YAAY,eAAe,eAAe,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG;AAAA,UAC5D,IAAI;AAAA,UACJ,WAAW;AAAA,UACX,kBAAkB;AAAA,QACpB,CAAC;AAAA,QACD,UAAU,CAAC,UAAU,cAAc;AAAA,MACrC;AACA,YAAM,OAAO;AAAA,QACX,KAAK;AAAA,QACL,UAAU,CAAC;AAAA,UACT,KAAK;AAAA,UACL,YAAY;AAAA,YACV,IAAI;AAAA,UACN;AAAA,UACA,UAAU,QAAQ,QAAQ;AAAA,QAC5B,GAAG,OAAO;AAAA,MACZ;AACA,eAAS,KAAK,MAAM;AAAA,QAClB,KAAK;AAAA,QACL,YAAY,eAAe;AAAA,UACzB,MAAM;AAAA,UACN,aAAa,QAAQ,OAAO,QAAQ,GAAG;AAAA,UACvC,MAAM,QAAQ,OAAO,QAAQ,GAAG;AAAA,QAClC,GAAG,SAAS;AAAA,MACd,CAAC;AACD,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,uBAAuB;AAAA,EACzB,SAASA,YAAW;AAClB,QAAI,eAAe;AACnB,QAAI,OAAO,YAAY;AACrB,qBAAe,OAAO,WAAW,kCAAkC,EAAE;AAAA,IACvE;AACA,IAAAA,WAAU,sBAAsB,WAAY;AAC1C,YAAM,YAAY,CAAC;AACnB,YAAM,OAAO;AAAA,QACX,MAAM;AAAA,MACR;AACA,YAAM,iBAAiB;AAAA,QACrB,eAAe;AAAA,QACf,aAAa;AAAA,QACb,KAAK;AAAA,MACP;AAGA,gBAAU,KAAK;AAAA,QACb,KAAK;AAAA,QACL,YAAY,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,UACvD,GAAG;AAAA,QACL,CAAC;AAAA,MACH,CAAC;AACD,YAAM,kBAAkB,eAAe,eAAe,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG;AAAA,QAC7E,eAAe;AAAA,MACjB,CAAC;AACD,YAAM,MAAM;AAAA,QACV,KAAK;AAAA,QACL,YAAY,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,UACvD,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,GAAG;AAAA,QACL,CAAC;AAAA,QACD,UAAU,CAAC;AAAA,MACb;AACA,UAAI,CAAC,cAAc;AACjB,YAAI,SAAS,KAAK;AAAA,UAChB,KAAK;AAAA,UACL,YAAY,eAAe,eAAe,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG;AAAA,YACjE,eAAe;AAAA,YACf,QAAQ;AAAA,UACV,CAAC;AAAA,QACH,GAAG;AAAA,UACD,KAAK;AAAA,UACL,YAAY,eAAe,eAAe,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG;AAAA,YAClE,QAAQ;AAAA,UACV,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,gBAAU,KAAK,GAAG;AAClB,gBAAU,KAAK;AAAA,QACb,KAAK;AAAA,QACL,YAAY,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,UACvD,SAAS;AAAA,UACT,GAAG;AAAA,QACL,CAAC;AAAA,QACD,UAAU,eAAe,CAAC,IAAI,CAAC;AAAA,UAC7B,KAAK;AAAA,UACL,YAAY,eAAe,eAAe,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG;AAAA,YAClE,QAAQ;AAAA,UACV,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AACD,UAAI,CAAC,cAAc;AAEjB,kBAAU,KAAK;AAAA,UACb,KAAK;AAAA,UACL,YAAY,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,YACvD,SAAS;AAAA,YACT,GAAG;AAAA,UACL,CAAC;AAAA,UACD,UAAU,CAAC;AAAA,YACT,KAAK;AAAA,YACL,YAAY,eAAe,eAAe,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG;AAAA,cAClE,QAAQ;AAAA,YACV,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,aAAO;AAAA,QACL,KAAK;AAAA,QACL,YAAY;AAAA,UACV,SAAS;AAAA,QACX;AAAA,QACA,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,aAAa;AAAA,EACf,QAAQ;AACN,WAAO;AAAA,MACL,oBAAoB,aAAa,MAAM;AACrC,cAAM,aAAa,KAAK,aAAa,gBAAgB;AACrD,cAAM,SAAS,eAAe,OAAO,QAAQ,eAAe,KAAK,OAAO;AACxE,oBAAY,QAAQ,IAAI;AACxB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,UAAU,CAAC,WAAW,iBAAiB,QAAQ,eAAe,YAAY,gBAAgB,oBAAoB,iBAAiB,OAAO,sBAAsB,UAAU;AAE1K,gBAAgB,SAAS;AAAA,EACvB,WAAW;AACb,CAAC;AACD,IAAM,WAAW,IAAI;AACrB,IAAM,WAAW,IAAI;AACrB,IAAM,YAAY,IAAI;AACtB,IAAM,QAAQ,IAAI;AAClB,IAAM,UAAU,IAAI;AACpB,IAAM,uBAAuB,IAAI;AACjC,IAAM,WAAW,IAAI;AACrB,IAAM,OAAO,IAAI;AACjB,IAAM,QAAQ,IAAI;AAClB,IAAM,OAAO,IAAI;AACjB,IAAM,UAAU,IAAI;", "names": ["r", "t", "o", "e", "p", "css", "styles", "s", "bindInternal4", "counter", "icon", "arr", "style", "mask", "content", "end", "providers"]}