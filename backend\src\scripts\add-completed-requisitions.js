const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: '*********',
  port: 3306,
  user: 'root',
  password: 'mysql_ycMQCy',
  database: 'mes'
};

async function addCompletedRequisitions() {
  let connection;
  
  try {
    console.log('连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    
    // 检查当前已完成的采购申请数量
    const [currentCompleted] = await connection.execute(`
      SELECT COUNT(*) as count 
      FROM purchase_requisitions 
      WHERE status = 'completed' 
      AND request_date >= DATE_FORMAT(NOW(), '%Y-%m-01')
    `);
    
    console.log(`当前本月已完成的采购申请数量: ${currentCompleted[0].count}`);
    
    if (currentCompleted[0].count < 5) {
      console.log('添加测试数据...');
      
      // 获取当前月份的日期范围
      const now = new Date();
      const currentMonth = now.getMonth() + 1;
      const currentYear = now.getFullYear();
      
      // 生成本月的几个日期
      const dates = [
        `${currentYear}-${currentMonth.toString().padStart(2, '0')}-05`,
        `${currentYear}-${currentMonth.toString().padStart(2, '0')}-10`,
        `${currentYear}-${currentMonth.toString().padStart(2, '0')}-15`,
        `${currentYear}-${currentMonth.toString().padStart(2, '0')}-20`,
        `${currentYear}-${currentMonth.toString().padStart(2, '0')}-25`
      ];
      
      // 生成采购申请编号的函数
      function generateRequisitionNo() {
        const now = new Date();
        const year = now.getFullYear();
        const month = (now.getMonth() + 1).toString().padStart(2, '0');
        const day = now.getDate().toString().padStart(2, '0');
        const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
        return `PR${year}${month}${day}${random}`;
      }
      
      // 插入已完成的采购申请
      for (let i = 0; i < 5; i++) {
        const requisitionNo = generateRequisitionNo();
        const requestDate = dates[i];
        
        await connection.execute(`
          INSERT INTO purchase_requisitions 
          (requisition_number, request_date, requester, real_name, remarks, status, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, 'completed', NOW(), NOW())
        `, [
          requisitionNo,
          requestDate,
          'admin',
          '系统管理员',
          `测试已完成采购申请 - ${i + 1}`
        ]);
        
        console.log(`已添加采购申请: ${requisitionNo}, 日期: ${requestDate}`);
      }
      
      // 再次检查
      const [newCompleted] = await connection.execute(`
        SELECT COUNT(*) as count 
        FROM purchase_requisitions 
        WHERE status = 'completed' 
        AND request_date >= DATE_FORMAT(NOW(), '%Y-%m-01')
      `);
      
      console.log(`现在本月已完成的采购申请数量: ${newCompleted[0].count}`);
    } else {
      console.log('已有足够的测试数据，无需添加');
    }
    
  } catch (error) {
    console.error('操作失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 运行脚本
addCompletedRequisitions();
