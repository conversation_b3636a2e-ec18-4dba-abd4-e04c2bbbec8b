-- 更新现金交易表，添加创建者字段
ALTER TABLE cash_transactions 
ADD COLUMN created_by INT NULL COMMENT '创建者ID' AFTER transaction_number,
ADD INDEX idx_created_by (created_by);

-- 添加外键约束（如果users表存在）
-- ALTER TABLE cash_transactions 
-- ADD CONSTRAINT fk_cash_transactions_created_by 
-- FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL;

-- 更新现有记录的创建者为系统用户（ID=1，如果存在）
UPDATE cash_transactions SET created_by = 1 WHERE created_by IS NULL;

-- 添加更多索引优化查询性能
ALTER TABLE cash_transactions 
ADD INDEX idx_amount (amount),
ADD INDEX idx_created_at (created_at),
ADD INDEX idx_updated_at (updated_at);

-- 创建现金交易分类表（可选，用于更好的分类管理）
CREATE TABLE IF NOT EXISTS cash_transaction_categories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  type ENUM('income', 'expense') NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  sort_order INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_type (type),
  INDEX idx_is_active (is_active),
  INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认分类数据
INSERT INTO cash_transaction_categories (code, name, type, description, sort_order) VALUES
('sales', '销售收入', 'income', '产品或服务销售收入', 1),
('other_income', '其他收入', 'income', '除销售外的其他收入', 2),
('office', '办公费用', 'expense', '办公用品、文具等费用', 1),
('travel', '差旅费', 'expense', '出差、交通、住宿等费用', 2),
('meal', '餐饮费', 'expense', '员工餐饮、招待费用', 3),
('other_expense', '其他支出', 'expense', '其他各类支出费用', 4)
ON DUPLICATE KEY UPDATE 
name = VALUES(name),
description = VALUES(description),
sort_order = VALUES(sort_order);

-- 创建现金交易附件表（可选，用于存储交易相关附件）
CREATE TABLE IF NOT EXISTS cash_transaction_attachments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  transaction_id INT NOT NULL,
  file_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_size INT NOT NULL,
  file_type VARCHAR(100) NOT NULL,
  uploaded_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_transaction_id (transaction_id),
  INDEX idx_uploaded_by (uploaded_by),
  FOREIGN KEY (transaction_id) REFERENCES cash_transactions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建现金交易审批表（可选，用于交易审批流程）
CREATE TABLE IF NOT EXISTS cash_transaction_approvals (
  id INT AUTO_INCREMENT PRIMARY KEY,
  transaction_id INT NOT NULL,
  approver_id INT NOT NULL,
  status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
  comments TEXT,
  approved_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_transaction_id (transaction_id),
  INDEX idx_approver_id (approver_id),
  INDEX idx_status (status),
  FOREIGN KEY (transaction_id) REFERENCES cash_transactions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
