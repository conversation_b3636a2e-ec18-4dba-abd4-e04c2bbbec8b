const express = require('express');
const router = express.Router();
const baseDataController = require('../controllers/baseDataController');
const productCategoryController = require('../controllers/productCategoryController');
const { authenticateToken } = require('../middleware/auth');
const multer = require('multer');
const path = require('path');

// 配置文件上传存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, '../../uploads/'));
  },
  filename: function (req, file, cb) {
    // 创建唯一文件名以避免覆盖
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 } // 限制10MB
});

// 文件上传路由
router.post('/upload', authenticateToken, upload.single('file'), baseDataController.uploadFile);

// 物料管理路由
router.get('/materials', authenticateToken, baseDataController.getAllMaterials);
router.get('/materials/options', authenticateToken, baseDataController.getMaterialOptions);
router.get('/materials/next-code', authenticateToken, baseDataController.getNextMaterialCode);
router.get('/materials/:id', authenticateToken, baseDataController.getMaterialById);
router.post('/materials', authenticateToken, baseDataController.createMaterial);
router.post('/materials/batch', authenticateToken, baseDataController.getMaterialsByIds); // 批量获取物料
router.put('/materials/:id', authenticateToken, baseDataController.updateMaterial);
router.delete('/materials/:id', authenticateToken, baseDataController.deleteMaterial);
// 添加导入物料的路由
router.post('/materials/import', authenticateToken, baseDataController.importMaterials);
// 添加导出物料的路由
router.post('/materials/export', authenticateToken, baseDataController.exportMaterials);

// BOM管理路由
router.get('/boms', authenticateToken, baseDataController.getAllBoms);
// 添加BOM导出路由（必须在:id路由之前）
router.get('/boms/export', authenticateToken, baseDataController.exportBoms);
// 添加BOM导入路由
router.post('/boms/import', authenticateToken, upload.single('file'), baseDataController.importBoms);
// 添加零部件定位路由
router.get('/boms/locate/:partCode', authenticateToken, baseDataController.locatePart);
router.get('/boms/:id', authenticateToken, baseDataController.getBomById);
router.post('/boms', authenticateToken, baseDataController.createBom);
router.put('/boms/:id', authenticateToken, baseDataController.updateBom);
router.delete('/boms/:id', authenticateToken, baseDataController.deleteBom);
// 添加BOM审核路由
router.put('/boms/:id/approve', authenticateToken, baseDataController.approveBom);
// 添加BOM反审核路由
router.put('/boms/:id/unapprove', authenticateToken, baseDataController.unapproveBom);

// 获取物料的BOM信息
router.get('/materials/:id/bom', authenticateToken, baseDataController.getMaterialBom);
// 根据产品ID获取BOM信息
router.get('/products/:id/bom', authenticateToken, baseDataController.getBomByProductId);

// 客户管理路由
router.get('/customers', authenticateToken, baseDataController.getAllCustomers);
router.get('/customers/:id', authenticateToken, baseDataController.getCustomerById);
router.post('/customers', authenticateToken, baseDataController.createCustomer);
router.put('/customers/:id', authenticateToken, baseDataController.updateCustomer);
router.delete('/customers/:id', authenticateToken, baseDataController.deleteCustomer);

// 供应商管理路由
router.get('/suppliers', authenticateToken, baseDataController.getAllSuppliers);
router.get('/suppliers/options', authenticateToken, baseDataController.getSupplierOptions);
router.get('/suppliers/:id', authenticateToken, baseDataController.getSupplierById);
router.post('/suppliers', authenticateToken, baseDataController.createSupplier);
router.put('/suppliers/:id', authenticateToken, baseDataController.updateSupplier);
router.delete('/suppliers/:id', authenticateToken, baseDataController.deleteSupplier);

// 产品分类管理路由
router.get('/categories', authenticateToken, baseDataController.getAllCategories);
router.get('/categories/:id', authenticateToken, baseDataController.getCategoryById);
router.post('/categories', authenticateToken, baseDataController.createCategory);
router.put('/categories/:id', authenticateToken, baseDataController.updateCategory);
router.delete('/categories/:id', authenticateToken, baseDataController.deleteCategory);

// 产品单位管理路由
router.get('/units', authenticateToken, baseDataController.getAllUnits);
router.get('/units/:id', authenticateToken, baseDataController.getUnitById);
router.post('/units', authenticateToken, baseDataController.createUnit);
router.put('/units/:id', authenticateToken, baseDataController.updateUnit);
router.delete('/units/:id', authenticateToken, baseDataController.deleteUnit);

// 库位管理路由
router.get('/locations', authenticateToken, baseDataController.getAllLocations);
router.get('/locations/:id', authenticateToken, baseDataController.getLocationById);
router.post('/locations', authenticateToken, baseDataController.createLocation);
router.put('/locations/:id', authenticateToken, baseDataController.updateLocation);
router.delete('/locations/:id', authenticateToken, baseDataController.deleteLocation);

// 仓库管理路由
router.get('/warehouses', authenticateToken, baseDataController.getWarehouses);

// 工序模板管理路由
router.get('/process-templates', authenticateToken, baseDataController.getAllProcessTemplates);
router.get('/process-templates/:id', authenticateToken, baseDataController.getProcessTemplateById);
router.post('/process-templates', authenticateToken, baseDataController.createProcessTemplate);
router.put('/process-templates/:id', authenticateToken, baseDataController.updateProcessTemplate);
router.put('/process-templates/:id/status', authenticateToken, baseDataController.updateProcessTemplateStatus);
router.delete('/process-templates/:id', authenticateToken, baseDataController.deleteProcessTemplate);
router.get('/products/:id/process-template', authenticateToken, baseDataController.getProcessTemplateByProductId);

// 产品大类管理路由
router.get('/product-categories', authenticateToken, productCategoryController.getAllProductCategories);
router.get('/product-categories/options', authenticateToken, productCategoryController.getProductCategoryOptions);
router.get('/product-categories/statistics', authenticateToken, productCategoryController.getStatistics);
router.get('/product-categories/:id', authenticateToken, productCategoryController.getProductCategoryById);
router.post('/product-categories', authenticateToken, productCategoryController.createProductCategory);
router.put('/product-categories/:id', authenticateToken, productCategoryController.updateProductCategory);
router.delete('/product-categories/:id', authenticateToken, productCategoryController.deleteProductCategory);

// 物料来源管理路由
router.get('/material-sources', authenticateToken, baseDataController.getAllMaterialSources);
router.get('/material-sources/statistics', authenticateToken, baseDataController.getMaterialSourceStatistics);
router.get('/material-sources/:id', authenticateToken, baseDataController.getMaterialSourceById);
router.post('/material-sources', authenticateToken, baseDataController.createMaterialSource);
router.put('/material-sources/:id', authenticateToken, baseDataController.updateMaterialSource);
router.delete('/material-sources/:id', authenticateToken, baseDataController.deleteMaterialSource);

module.exports = router;