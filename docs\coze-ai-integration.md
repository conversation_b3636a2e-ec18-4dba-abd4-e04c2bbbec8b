# Coze AI 集成文档

## 概述

本文档描述了如何在ERP系统的采购AI助手中集成Coze AI服务。

## 配置信息

### API密钥
```
pat_pVGqTQOURRYxEvVrYWMKtyAoX9fxK1f87h8lZuaGzAKOjose26XU1CoO7ena1WRd
```

### Bot ID
```
7445818717247561748
```

### API端点
```
https://api.coze.cn/v1/chat
```

## 功能特性

### 1. AI服务选择
- 用户可以在AI助手界面右上角选择不同的AI服务
- 支持的服务：
  - Ollama本地模型
  - SiliconFlow云端模型
  - **Coze智能助手** (新增)

### 2. Coze集成模式
- **REST API模式**: 直接调用Coze API，适合自定义集成
- **Web SDK模式**: 使用官方Web SDK，提供更丰富的交互体验
- 用户可以在界面上动态切换模式

### 3. 智能对话
- 支持自然语言对话
- 专业的采购咨询服务
- 实时响应用户问题

### 4. 采购功能集成
- 创建采购申请
- 查询订单状态
- 供应商管理
- 价格分析

## 技术实现

### 前端实现

#### 1. 服务配置
```javascript
const aiConfig = ref({
  selectedService: 'ollama',
  services: {
    coze: {
      connected: true,
      model: 'coze-bot',
      server: 'api.coze.cn',
      name: 'Coze智能助手',
      apiKey: 'pat_pVGqTQOURRYxEvVrYWMKtyAoX9fxK1f87h8lZuaGzAKOjose26XU1CoO7ena1WRd',
      botId: '7445818717247561748'
    }
  }
})
```

#### 2. API调用
```javascript
const handleCozeResponse = async (userQuestion) => {
  const response = await fetch('https://api.coze.cn/v1/chat', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${cozeConfig.apiKey}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      bot_id: cozeConfig.botId,
      user_id: 'user_' + Date.now(),
      stream: false,
      auto_save_history: true,
      additional_messages: [
        {
          role: 'user',
          content: userQuestion,
          content_type: 'text'
        }
      ]
    })
  })
}
```

### 后端实现

#### 1. 配置参数
```javascript
const COZE_URL = process.env.COZE_URL || 'https://api.coze.cn/v1';
const COZE_API_KEY = process.env.COZE_API_KEY || 'pat_pVGqTQOURRYxEvVrYWMKtyAoX9fxK1f87h8lZuaGzAKOjose26XU1CoO7ena1WRd';
const COZE_BOT_ID = process.env.COZE_BOT_ID || '7445818717247561748';
```

#### 2. API函数
```javascript
const queryCoze = async (req, res) => {
  const response = await axios.post(`${COZE_URL}/chat`, {
    bot_id: COZE_BOT_ID,
    user_id: 'user_' + Date.now(),
    stream: false,
    auto_save_history: true,
    additional_messages: [
      {
        role: 'user',
        content: prompt,
        content_type: 'text'
      }
    ]
  }, {
    headers: {
      'Authorization': `Bearer ${COZE_API_KEY}`,
      'Content-Type': 'application/json'
    }
  })
}
```

## 使用方法

### 1. 访问AI助手
```
http://localhost:3000/purchase/ai-assistant
```

### 2. 选择Coze服务
- 点击右上角的AI服务选择器
- 选择"Coze智能助手"
- 系统会显示切换成功的消息

### 3. 开始对话
- 在输入框中输入问题
- 按回车或点击发送按钮
- 等待Coze AI的回复

## 测试示例

### 基础对话测试
```
用户: 你好，请介绍一下你的功能
Coze: 您好！我是基于Coze平台的智能助手，可以为您提供专业的采购咨询服务...
```

### 采购功能测试
```
用户: 帮我创建一个电路板的采购申请，数量50
Coze: 我已理解您的需求，正在为您创建采购申请...
```

## 故障排除

### 1. API连接失败
- 检查网络连接
- 验证API密钥是否正确
- 确认Bot ID是否有效

### 2. 响应格式错误
- 检查API版本是否匹配
- 验证请求参数格式
- 查看控制台错误日志

### 3. 权限问题
- 确认API密钥权限
- 检查Bot配置状态
- 验证用户访问权限

## 环境变量配置

在生产环境中，建议使用环境变量配置敏感信息：

```bash
# .env 文件
COZE_URL=https://api.coze.cn/v1
COZE_API_KEY=your_actual_api_key_here
COZE_BOT_ID=your_actual_bot_id_here
```

## 安全注意事项

1. **API密钥保护**: 不要在前端代码中硬编码API密钥
2. **请求限制**: 实施适当的请求频率限制
3. **数据隐私**: 确保用户数据的安全传输和存储
4. **错误处理**: 实施完善的错误处理机制

## 更新日志

### v1.0.0 (2025-08-04)
- 初始集成Coze AI服务
- 支持基础对话功能
- 集成采购助手功能
- 添加服务切换功能
