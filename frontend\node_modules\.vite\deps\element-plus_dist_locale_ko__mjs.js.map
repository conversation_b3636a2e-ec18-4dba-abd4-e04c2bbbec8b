{"version": 3, "sources": ["../../element-plus/dist/locale/ko.mjs"], "sourcesContent": ["/*! Element Plus v2.9.7 */\n\nvar ko = {\n  name: \"ko\",\n  el: {\n    breadcrumb: {\n      label: \"Breadcrumb\"\n    },\n    colorpicker: {\n      confirm: \"\\uD655\\uC778\",\n      clear: \"\\uCD08\\uAE30\\uD654\",\n      defaultLabel: \"\\uC0C9\\uC0C1 \\uC120\\uD0DD\\uAE30\",\n      description: \"\\uD604\\uC7AC \\uC0C9\\uC0C1\\uC740 {color}\\uC785\\uB2C8\\uB2E4. Enter \\uD0A4\\uB97C \\uB20C\\uB7EC \\uC0C8 \\uC0C9\\uC0C1\\uC744 \\uC120\\uD0DD\\uD569\\uB2C8\\uB2E4.\"\n    },\n    datepicker: {\n      now: \"\\uC9C0\\uAE08\",\n      today: \"\\uC624\\uB298\",\n      cancel: \"\\uCDE8\\uC18C\",\n      clear: \"\\uCD08\\uAE30\\uD654\",\n      confirm: \"\\uD655\\uC778\",\n      dateTablePrompt: \"\\uD654\\uC0B4\\uD45C \\uD0A4\\uB97C \\uC0AC\\uC6A9\\uD558\\uACE0 Enter\\uB97C \\uB20C\\uB7EC \\uB0A0\\uC9DC\\uB97C \\uC120\\uD0DD\\uD558\\uC2ED\\uC2DC\\uC624.\",\n      monthTablePrompt: \"\\uD654\\uC0B4\\uD45C \\uD0A4\\uB97C \\uC0AC\\uC6A9\\uD558\\uACE0 Enter\\uB97C \\uB20C\\uB7EC \\uC6D4\\uC744 \\uC120\\uD0DD\\uD569\\uB2C8\\uB2E4.\",\n      yearTablePrompt: \"\\uD654\\uC0B4\\uD45C \\uD0A4\\uB97C \\uC0AC\\uC6A9\\uD558\\uACE0 Enter \\uD0A4\\uB97C \\uB20C\\uB7EC \\uC5F0\\uB3C4\\uB97C \\uC120\\uD0DD\\uD569\\uB2C8\\uB2E4.\",\n      selectDate: \"\\uB0A0\\uC9DC \\uC120\\uD0DD\",\n      selectTime: \"\\uC2DC\\uAC04 \\uC120\\uD0DD\",\n      startDate: \"\\uC2DC\\uC791 \\uB0A0\\uC9DC\",\n      startTime: \"\\uC2DC\\uC791 \\uC2DC\\uAC04\",\n      endDate: \"\\uC885\\uB8CC \\uB0A0\\uC9DC\",\n      endTime: \"\\uC885\\uB8CC \\uC2DC\\uAC04\",\n      prevYear: \"\\uC9C0\\uB09C\\uD574\",\n      nextYear: \"\\uB2E4\\uC74C\\uD574\",\n      prevMonth: \"\\uC9C0\\uB09C\\uB2EC\",\n      nextMonth: \"\\uB2E4\\uC74C\\uB2EC\",\n      year: \"\\uB144\",\n      month1: \"1\\uC6D4\",\n      month2: \"2\\uC6D4\",\n      month3: \"3\\uC6D4\",\n      month4: \"4\\uC6D4\",\n      month5: \"5\\uC6D4\",\n      month6: \"6\\uC6D4\",\n      month7: \"7\\uC6D4\",\n      month8: \"8\\uC6D4\",\n      month9: \"9\\uC6D4\",\n      month10: \"10\\uC6D4\",\n      month11: \"11\\uC6D4\",\n      month12: \"12\\uC6D4\",\n      weeks: {\n        sun: \"\\uC77C\",\n        mon: \"\\uC6D4\",\n        tue: \"\\uD654\",\n        wed: \"\\uC218\",\n        thu: \"\\uBAA9\",\n        fri: \"\\uAE08\",\n        sat: \"\\uD1A0\"\n      },\n      months: {\n        jan: \"1\\uC6D4\",\n        feb: \"2\\uC6D4\",\n        mar: \"3\\uC6D4\",\n        apr: \"4\\uC6D4\",\n        may: \"5\\uC6D4\",\n        jun: \"6\\uC6D4\",\n        jul: \"7\\uC6D4\",\n        aug: \"8\\uC6D4\",\n        sep: \"9\\uC6D4\",\n        oct: \"10\\uC6D4\",\n        nov: \"11\\uC6D4\",\n        dec: \"12\\uC6D4\"\n      }\n    },\n    inputNumber: {\n      decrease: \"\\uAC12 \\uC99D\\uAC00\",\n      increase: \"\\uAC12 \\uAC10\\uC18C\"\n    },\n    select: {\n      loading: \"\\uBD88\\uB7EC\\uC624\\uB294 \\uC911\",\n      noMatch: \"\\uAC80\\uC0C9\\uB41C \\uB370\\uC774\\uD130 \\uC5C6\\uC74C\",\n      noData: \"\\uB370\\uC774\\uD130 \\uC5C6\\uC74C\",\n      placeholder: \"\\uC120\\uD0DD\"\n    },\n    mention: {\n      loading: \"\\uBD88\\uB7EC\\uC624\\uB294 \\uC911\"\n    },\n    dropdown: {\n      toggleDropdown: \"\\uB4DC\\uB86D\\uB2E4\\uC6B4 \\uC804\\uD658\"\n    },\n    cascader: {\n      noMatch: \"\\uAC80\\uC0C9\\uB41C \\uB370\\uC774\\uD130 \\uC5C6\\uC74C\",\n      loading: \"\\uBD88\\uB7EC\\uC624\\uB294 \\uC911\",\n      placeholder: \"\\uC120\\uD0DD\",\n      noData: \"\\uB370\\uC774\\uD130 \\uC5C6\\uC74C\"\n    },\n    pagination: {\n      goto: \"\",\n      pagesize: \"\\uAC74/\\uD398\\uC774\\uC9C0\",\n      total: \"\\uCD1D {total} \\uAC74\",\n      pageClassifier: \"\\uD398\\uC774\\uC9C0\\uB85C\",\n      page: \"\\uD398\\uC774\\uC9C0\",\n      prev: \"\\uC774\\uC804 \\uD398\\uC774\\uC9C0\\uB85C \\uC774\\uB3D9\",\n      next: \"\\uB2E4\\uC74C \\uD398\\uC774\\uC9C0\\uB85C \\uC774\\uB3D9\",\n      currentPage: \"\\uD398\\uC774\\uC9C0 {pager}\",\n      prevPages: \"\\uC774\\uC804 {pager} \\uD398\\uC774\\uC9C0\",\n      nextPages: \"\\uB2E4\\uC74C {pager} \\uD398\\uC774\\uC9C0\",\n      deprecationWarning: \"\\uB354 \\uC774\\uC0C1 \\uC0AC\\uC6A9\\uB418\\uC9C0 \\uC54A\\uB294 \\uB3D9\\uC791\\uC774 \\uAC10\\uC9C0\\uB418\\uC5C8\\uC2B5\\uB2C8\\uB2E4. \\uC790\\uC138\\uD55C \\uB0B4\\uC6A9\\uC740 el-pagination \\uBB38\\uC11C\\uB97C \\uCC38\\uC870\\uD558\\uC138\\uC694.\"\n    },\n    dialog: {\n      close: \"\\uB300\\uD654 \\uC0C1\\uC790 \\uB2EB\\uAE30\"\n    },\n    drawer: {\n      close: \"\\uB300\\uD654 \\uC0C1\\uC790 \\uB2EB\\uAE30\"\n    },\n    messagebox: {\n      title: \"\\uBA54\\uC2DC\\uC9C0\",\n      confirm: \"\\uD655\\uC778\",\n      cancel: \"\\uCDE8\\uC18C\",\n      error: \"\\uC62C\\uBC14\\uB974\\uC9C0 \\uC54A\\uC740 \\uC785\\uB825\",\n      close: \"\\uB300\\uD654 \\uC0C1\\uC790 \\uB2EB\\uAE30\"\n    },\n    upload: {\n      deleteTip: \"Delete \\uD0A4\\uB97C \\uB20C\\uB7EC \\uC0AD\\uC81C\",\n      delete: \"\\uC0AD\\uC81C\",\n      preview: \"\\uBBF8\\uB9AC\\uBCF4\\uAE30\",\n      continue: \"\\uACC4\\uC18D\\uD558\\uAE30\"\n    },\n    slider: {\n      defaultLabel: \"{min}\\uACFC {max} \\uC0AC\\uC774\\uC758 \\uC2AC\\uB77C\\uC774\\uB354\",\n      defaultRangeStartLabel: \"\\uC2DC\\uC791 \\uAC12 \\uC120\\uD0DD\",\n      defaultRangeEndLabel: \"\\uC885\\uB8CC \\uAC12 \\uC120\\uD0DD\"\n    },\n    table: {\n      emptyText: \"\\uB370\\uC774\\uD130 \\uC5C6\\uC74C\",\n      confirmFilter: \"\\uD655\\uC778\",\n      resetFilter: \"\\uCD08\\uAE30\\uD654\",\n      clearFilter: \"\\uC804\\uCCB4\",\n      sumText: \"\\uD569\\uACC4\"\n    },\n    tour: {\n      next: \"\\uB2E4\\uC74C\",\n      previous: \"\\uC774\\uC804\",\n      finish: \"\\uC885\\uB8CC\"\n    },\n    tree: {\n      emptyText: \"\\uB370\\uC774\\uD130 \\uC5C6\\uC74C\"\n    },\n    transfer: {\n      noMatch: \"\\uAC80\\uC0C9\\uB41C \\uB370\\uC774\\uD130 \\uC5C6\\uC74C\",\n      noData: \"\\uB370\\uC774\\uD130 \\uC5C6\\uC74C\",\n      titles: [\"\\uB9AC\\uC2A4\\uD2B8 1\", \"\\uB9AC\\uC2A4\\uD2B8 2\"],\n      filterPlaceholder: \"\\uAC80\\uC0C9\\uC5B4\\uB97C \\uC785\\uB825\\uD558\\uC138\\uC694\",\n      noCheckedFormat: \"\\uCD1D {total} \\uAC74\",\n      hasCheckedFormat: \"{checked}/{total} \\uC120\\uD0DD\\uB428\"\n    },\n    image: {\n      error: \"\\uBD88\\uB7EC\\uC624\\uAE30 \\uC2E4\\uD328\"\n    },\n    pageHeader: {\n      title: \"\\uB4A4\\uB85C\"\n    },\n    popconfirm: {\n      confirmButtonText: \"\\uC608\",\n      cancelButtonText: \"\\uC544\\uB2C8\\uC624\"\n    },\n    carousel: {\n      leftArrow: \"Carousel arrow left\",\n      rightArrow: \"Carousel arrow right\",\n      indicator: \"Carousel switch to index {index}\"\n    }\n  }\n};\n\nexport { ko as default };\n"], "mappings": ";;;AAEA,IAAI,KAAK;AAAA,EACP,MAAM;AAAA,EACN,IAAI;AAAA,IACF,YAAY;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,OAAO;AAAA,MACP,cAAc;AAAA,MACd,aAAa;AAAA,IACf;AAAA,IACA,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,MACX,WAAW;AAAA,MACX,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,MACA,QAAQ;AAAA,QACN,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,aAAa;AAAA,IACf;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,QAAQ;AAAA,IACV;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,WAAW;AAAA,MACX,WAAW;AAAA,MACX,oBAAoB;AAAA,IACtB;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,MACN,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,MACN,cAAc;AAAA,MACd,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,IACxB;AAAA,IACA,OAAO;AAAA,MACL,WAAW;AAAA,MACX,eAAe;AAAA,MACf,aAAa;AAAA,MACb,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,MACV,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,WAAW;AAAA,IACb;AAAA,IACA,UAAU;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ,CAAC,SAAwB,OAAsB;AAAA,MACvD,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,IACpB;AAAA,IACA,OAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,IACpB;AAAA,IACA,UAAU;AAAA,MACR,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,IACb;AAAA,EACF;AACF;", "names": []}