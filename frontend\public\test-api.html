<!DOCTYPE html>
<html>
<head>
    <title>API测试</title>
</head>
<body>
    <h1>客户API测试</h1>
    <button onclick="testCustomersAPI()">测试客户API</button>
    <div id="result"></div>

    <script>
        async function testCustomersAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在测试...';
            
            try {
                // 获取token
                const token = localStorage.getItem('token');
                console.log('Token:', token);
                
                const response = await fetch('/api/baseData/customers?page=1&pageSize=10', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                const data = await response.json();
                console.log('Response data:', data);
                
                resultDiv.innerHTML = `
                    <h3>API响应结果:</h3>
                    <p>状态码: ${response.status}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
            } catch (error) {
                console.error('API测试失败:', error);
                resultDiv.innerHTML = `
                    <h3>API测试失败:</h3>
                    <p style="color: red;">${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
