const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: '*********',
  port: 3306,
  user: 'root',
  password: 'mysql_ycMQCy',
  database: 'mes'
};

async function testRequisitionsQuery() {
  let connection;
  
  try {
    console.log('连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    
    // 获取当前月份的开始和结束日期
    const now = new Date();
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const currentMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    const startDate = currentMonthStart.toISOString().split('T')[0];
    const endDate = currentMonthEnd.toISOString().split('T')[0];
    
    console.log('查询参数:');
    console.log('- startDate:', startDate);
    console.log('- endDate:', endDate);
    console.log('- status: completed');
    
    // 模拟后端API的查询逻辑
    let query = `
      SELECT r.*, u.real_name as user_real_name, COUNT(*) OVER() as total_count
      FROM purchase_requisitions r
      LEFT JOIN users u ON r.requester = u.username
      WHERE 1=1
    `;
    
    const queryParams = [];
    
    // 添加日期范围过滤
    query += ` AND r.request_date >= ?`;
    queryParams.push(startDate);
    
    query += ` AND r.request_date <= ?`;
    queryParams.push(endDate);
    
    // 添加状态过滤
    query += ` AND r.status = ?`;
    queryParams.push('completed');
    
    query += ` ORDER BY r.request_date DESC`;
    
    console.log('\n执行的SQL查询:');
    console.log(query);
    console.log('参数:', queryParams);
    
    const [results] = await connection.execute(query, queryParams);
    
    console.log('\n查询结果:');
    console.log('- 记录数量:', results.length);
    
    if (results.length > 0) {
      console.log('- 第一条记录:', {
        id: results[0].id,
        requisition_number: results[0].requisition_number,
        request_date: results[0].request_date,
        status: results[0].status,
        requester: results[0].requester
      });
    }
    
    // 检查所有状态的采购申请
    console.log('\n所有状态的采购申请统计:');
    const [statusStats] = await connection.execute(`
      SELECT status, COUNT(*) as count
      FROM purchase_requisitions
      WHERE request_date >= ? AND request_date <= ?
      GROUP BY status
    `, [startDate, endDate]);
    
    statusStats.forEach(stat => {
      console.log(`- ${stat.status}: ${stat.count}条`);
    });
    
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n数据库连接已关闭');
    }
  }
}

// 运行测试
testRequisitionsQuery();
