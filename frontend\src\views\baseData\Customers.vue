<template>
  <div class="customers-container">
    <div class="page-header">
      <h2>{{ $t('page.baseData.customers.title') }}</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon> {{ $t('page.baseData.customers.add') }}
      </el-button>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item :label="$t('page.baseData.customers.customerCode')">
          <el-input v-model="searchForm.code" :placeholder="$t('page.baseData.customers.customerCodePlaceholder')" clearable></el-input>
        </el-form-item>
        <el-form-item :label="$t('page.baseData.customers.customerName')">
          <el-input v-model="searchForm.name" :placeholder="$t('page.baseData.customers.customerNamePlaceholder')" clearable></el-input>
        </el-form-item>
        <el-form-item :label="$t('common.status')">
          <el-select v-model="searchForm.status" :placeholder="$t('page.baseData.materials.statusPlaceholder')" clearable>
            <el-option :value="'active'" :label="$t('page.baseData.materials.enabled')"></el-option>
            <el-option :value="'inactive'" :label="$t('page.baseData.materials.disabled')"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon> {{ $t('common.search') }}
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon> {{ $t('common.reset') }}
          </el-button>
          <el-button type="success" @click="handleExport">
            <el-icon><Download /></el-icon> {{ $t('common.export') }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计信息 -->
    <div class="statistics-row">
      <el-card class="stat-card" shadow="hover">
        <div class="stat-value">{{ stats.total || 0 }}</div>
        <div class="stat-label">{{ $t('page.baseData.customers.totalCustomers') }}</div>
      </el-card>
      <el-card class="stat-card" shadow="hover">
        <div class="stat-value">{{ stats.active || 0 }}</div>
        <div class="stat-label">{{ $t('page.baseData.customers.activeCustomers') }}</div>
      </el-card>
      <el-card class="stat-card" shadow="hover">
        <div class="stat-value">{{ stats.inactive || 0 }}</div>
        <div class="stat-label">{{ $t('page.baseData.customers.inactiveCustomers') }}</div>
      </el-card>
      <el-card class="stat-card" shadow="hover">
        <div class="stat-value">{{ stats.totalCredit || 0 }} {{ $t('common.currency') }}</div>
        <div class="stat-label">{{ $t('page.baseData.customers.creditLimit') }}</div>
      </el-card>
    </div>

    <!-- 表格区域 -->
    <el-card class="data-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        :max-height="tableHeight"
      >
        <el-table-column prop="code" :label="$t('page.baseData.customers.customerCode')" width="120">
          <template #default="scope">
            <span>{{ scope.row.code || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('page.baseData.customers.customerName')" min-width="200">
          <template #default="scope">
            <el-tooltip :content="scope.row.name" placement="top" :show-after="500">
              <span class="ellipsis-cell">{{ scope.row.name }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="contact_person" :label="$t('page.baseData.customers.contact')" width="120"></el-table-column>
        <el-table-column prop="contact_phone" :label="$t('page.baseData.customers.phone')" width="120"></el-table-column>
        <el-table-column prop="email" :label="$t('user.email')" min-width="180">
          <template #default="scope">
            <el-tooltip :content="scope.row.email" placement="top" :show-after="500">
              <span class="ellipsis-cell">{{ scope.row.email }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="status" :label="$t('common.status')" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
              {{ scope.row.status === 'active' ? $t('page.baseData.materials.enabled') : $t('page.baseData.materials.disabled') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="credit_limit" :label="$t('page.baseData.customers.creditLimit')" width="100"></el-table-column>
        <el-table-column prop="address" :label="$t('page.baseData.customers.address')" min-width="200">
          <template #default="scope">
            <el-tooltip :content="scope.row.address" placement="top" :show-after="500">
              <span class="ellipsis-cell">{{ scope.row.address }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="150">
          <template #default="scope">
            <el-tooltip :content="scope.row.remark" placement="top" :show-after="500">
              <span class="ellipsis-cell">{{ scope.row.remark }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">
              <el-icon><Edit /></el-icon> 编辑
            </el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">
              <el-icon><Delete /></el-icon> 删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="Math.max(parseInt(total) || 0, 1)"
          :page-sizes="[10, 20, 50, 100]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="客户编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入客户编码（如：C0001）" :disabled="isEdit">
            <template #append v-if="!isEdit">
              <el-button @click="generateCode">自动生成</el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="客户名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入客户名称"></el-input>
        </el-form-item>
        <el-form-item label="联系人" prop="contact_person">
          <el-input v-model="form.contact_person" placeholder="请输入联系人"></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="contact_phone">
          <el-input v-model="form.contact_phone" placeholder="请输入联系电话"></el-input>
        </el-form-item>
        <el-form-item label="电子邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入电子邮箱"></el-input>
        </el-form-item>
        <el-form-item label="信用额度" prop="credit_limit">
          <el-input-number v-model="form.credit_limit" :min="0" :precision="2" :step="100" placeholder="请输入信用额度"></el-input-number>
        </el-form-item>
        <el-form-item label="地址">
          <el-input v-model="form.address" type="textarea" :rows="2" placeholder="请输入地址"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio :value="'active'">启用</el-radio>
            <el-radio :value="'inactive'">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { baseDataApi } from '@/services/api';
import { Plus, Edit, Delete, Search, Refresh, Download } from '@element-plus/icons-vue';

// 数据加载状态
const loading = ref(false);

// 表格数据
const tableData = ref([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const tableHeight = ref('calc(100vh - 350px)');

// 统计数据
const stats = reactive({
  total: 0,
  active: 0,
  inactive: 0,
  totalCredit: 0
});

// 搜索表单
const searchForm = reactive({
  code: '',
  name: '',
  status: ''
});

// 新增/编辑表单
const formRef = ref(null);
const form = reactive({
  id: '',
  code: '',
  name: '',
  contact_person: '',
  contact_phone: '',
  email: '',
  address: '',
  credit_limit: 0,
  status: 'active',  // 使用字符串类型的status
  remark: ''
});

// 表单校验规则
const rules = {
  code: [{ required: true, message: '请输入客户编码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
  contact_person: [{ message: '请输入联系人', trigger: 'blur' }],
  contact_phone: [{ message: '请输入联系电话', trigger: 'blur' }],
  email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }]
};

// 对话框控制
const dialogVisible = ref(false);
const dialogTitle = ref('新增客户');
const isEdit = ref(false);

// 初始化
onMounted(() => {
  // 初始化时加载第一页数据
  currentPage.value = 1;
  pageSize.value = 10;
  fetchData();
});

// 导出数据
const handleExport = async () => {
  try {
    const response = await baseDataApi.exportCustomers({
      code: searchForm.code,
      name: searchForm.name,
      status: searchForm.status
    });
    // 处理文件下载
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', '客户列表.xlsx');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    ElMessage.success('导出成功');
  } catch (error) {
    ElMessage.error('导出失败');
  }
};

// 计算统计数据
const calculateStats = () => {
  // 确保tableData.value是数组
  if (!Array.isArray(tableData.value)) {
    tableData.value = [];
  }

  stats.total = tableData.value.length;
  stats.active = tableData.value.filter(item => item.status === 'active').length;
  stats.inactive = tableData.value.filter(item => item.status === 'inactive').length;

  // 计算总信用额度
  stats.totalCredit = tableData.value.reduce((total, customer) => {
    return total + (parseFloat(customer.credit_limit) || 0);
  }, 0).toFixed(2);
};

// 获取客户列表
const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      ...searchForm
    };
    
    // 移除空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key];
      }
    });
    
    const response = await baseDataApi.getCustomers(params);

    // 处理后端返回的不同数据格式
    if (response.data) {
      if (response.data.success && response.data.data) {
        // 新的响应格式：{ success: true, data: { list: [], total: 0 } }
        tableData.value = response.data.data.list || [];
        total.value = response.data.data.total || 0;
      } else if (response.data.list) {
        tableData.value = response.data.list;
        total.value = response.data.total || response.data.list.length;
      } else if (response.data.items) {
        tableData.value = response.data.items;
        total.value = response.data.total || response.data.items.length;
      } else if (response.data.data) {
        tableData.value = response.data.data;
        total.value = response.data.pagination?.total || response.data.total || response.data.data.length;
      } else if (Array.isArray(response.data)) {
        tableData.value = response.data;
        total.value = response.data.length;
      } else {
        tableData.value = [];
        total.value = 0;
      }
    } else {
      tableData.value = [];
      total.value = 0;
    }
    
    // 确保total不为0，以显示分页
    if (total.value === 0 && tableData.value.length > 0) {
      total.value = tableData.value.length;
    }
    
    // 计算统计数据
    calculateStats();
  } catch (error) {
    console.error('获取客户列表失败:', error);
    ElMessage.error('获取客户列表失败');
    tableData.value = [];
    total.value = 0;
    // 重新计算统计数据（此时tableData为空数组）
    calculateStats();
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  currentPage.value = 1;
  fetchData();
};

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = '';
  });
  currentPage.value = 1;
  fetchData();
};

// 分页相关
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1; // 重置到第一页
  fetchData();
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  // 强制执行数据获取，不使用缓存
  fetchData();
};

// 新增客户
const handleAdd = () => {
  dialogTitle.value = '新增客户';
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

// 编辑客户
const handleEdit = (row) => {
  dialogTitle.value = '编辑客户';
  isEdit.value = true;
  resetForm();
  Object.assign(form, {
    ...row,
    credit_limit: parseFloat(row.credit_limit) || 0
  });
  dialogVisible.value = true;
};

// 删除客户
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该客户吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await baseDataApi.deleteCustomer(row.id);
      ElMessage.success('删除成功');
      fetchData();
    } catch (error) {
      console.error('删除客户失败:', error);
      
      // 提取更详细的错误信息
      let errorMessage = '删除客户失败';
      
      if (error.response) {
        // 服务器响应了，但状态码不在 2xx 范围内
        console.error('服务器响应状态码:', error.response.status);
        console.error('服务器响应数据:', error.response.data);
        
        if (error.response.data && error.response.data.message) {
          errorMessage = `删除失败: ${error.response.data.message}`;
        } else if (error.response.status === 500) {
          errorMessage = '服务器内部错误，该客户可能已被其他数据引用，无法删除';
        } else if (error.response.status === 404) {
          errorMessage = '客户不存在或已被删除';
        } else if (error.response.status === 403) {
          errorMessage = '您没有权限删除此客户';
        }
      } else if (error.request) {
        // 请求已发送，但没有收到响应
        errorMessage = '服务器无响应，请检查网络连接';
      }
      
      ElMessage.error(errorMessage);
    }
  }).catch(() => {
    // 用户取消删除操作，不做任何处理
  });
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  
  Object.keys(form).forEach(key => {
    if (key === 'status') {
      form[key] = 'active';  // 使用字符串类型的status
    } else if (key === 'credit_limit') {
      form[key] = 0;
    } else {
      form[key] = '';
    }
  });
};

// 自动生成客户编码
const generateCode = async () => {
  try {
    // 获取最大的客户ID来生成编码
    const response = await baseDataApi.getCustomers({ pageSize: 1000 });
    let maxId = 0;

    if (response.data && response.data.success && response.data.data && response.data.data.list) {
      const customers = response.data.data.list;
      customers.forEach(customer => {
        if (customer.id > maxId) {
          maxId = customer.id;
        }
      });
    }

    // 生成新的编码
    const newCode = `C${String(maxId + 1).padStart(4, '0')}`;
    form.code = newCode;

    ElMessage.success(`已生成编码: ${newCode}`);
  } catch (error) {
    console.error('生成编码失败:', error);
    ElMessage.error('生成编码失败');
  }
};

// 提交表单
const submitForm = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 确保表单数据格式正确
        const formData = {
          ...form,
          name: form.name.trim(),
          contact_person: form.contact_person ? form.contact_person.trim() : '',
          contact_phone: form.contact_phone ? form.contact_phone.trim() : '',
          email: form.email ? form.email.trim() : '',
          address: form.address ? form.address.trim() : '',
          credit_limit: parseFloat(form.credit_limit) || 0,
          remark: form.remark ? form.remark.trim() : ''
        };

        if (isEdit.value) {
          // 编辑
          await baseDataApi.updateCustomer(form.id, formData);
          ElMessage.success('编辑成功');
        } else {
          // 新增
          await baseDataApi.createCustomer(formData);
          ElMessage.success('新增成功');
        }
        dialogVisible.value = false;
        fetchData();
      } catch (error) {
        console.error('保存客户失败:', error);
        if (error.response && error.response.data && error.response.data.message) {
          ElMessage.error(`保存失败: ${error.response.data.message}`);
        } else {
          ElMessage.error('保存客户失败');
        }
      }
    }
  });
};
</script>

<style scoped>
.customers-container {
  padding: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.search-card {
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}

.statistics-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 15px;
}

.stat-card {
  flex: 1;
  min-width: 150px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.data-card {
  margin-bottom: 20px;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.ellipsis-cell {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 优化表格内容显示 */
:deep(.el-table .cell) {
  word-break: break-word;
  line-height: 1.5;
}
</style>