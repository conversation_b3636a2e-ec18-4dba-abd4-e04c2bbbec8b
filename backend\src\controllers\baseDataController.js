const { materialModel, bomModel, customerModel, supplierModel, categoryModel, unitModel } = require('../models/baseData');
const locationsModel = require('../models/locations');
const { pool } = require('../config/db');
const path = require('path');
const fs = require('fs');

// 添加请求计数，帮助调试
let materialsRequestCount = 0;

const baseDataController = {
  // 分类管理
  async getAllCategories(req, res) {
    try {
      const result = await categoryModel.getAllCategories(req.query);
      res.json({
        list: result,
        total: result.length
      });
    } catch (error) {
      console.error('获取分类列表失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async getCategoryById(req, res) {
    try {
      const category = await categoryModel.getCategoryById(req.params.id);
      if (category) {
        res.json(category);
      } else {
        res.status(404).json({ message: '分类不存在' });
      }
    } catch (error) {
      console.error('获取分类详情失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async createCategory(req, res) {
    try {
      const newCategory = await categoryModel.createCategory(req.body);
      res.status(201).json(newCategory);
    } catch (error) {
      console.error('创建分类失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async updateCategory(req, res) {
    try {
      const updatedCategory = await categoryModel.updateCategory(req.params.id, req.body);
      res.json(updatedCategory);
    } catch (error) {
      console.error('更新分类失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async deleteCategory(req, res) {
    try {
      await categoryModel.deleteCategory(req.params.id);
      res.status(204).end();
    } catch (error) {
      console.error('删除分类失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  // 单位管理
  async getAllUnits(req, res) {
    try {
      const result = await unitModel.getAllUnits(req.query);
      res.json({
        list: result,
        total: result.length
      });
    } catch (error) {
      console.error('获取单位列表失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async getUnitById(req, res) {
    try {
      const unit = await unitModel.getUnitById(req.params.id);
      if (unit) {
        res.json(unit);
      } else {
        res.status(404).json({ message: '单位不存在' });
      }
    } catch (error) {
      console.error('获取单位详情失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async createUnit(req, res) {
    try {
      const newUnit = await unitModel.createUnit(req.body);
      res.status(201).json(newUnit);
    } catch (error) {
      console.error('创建单位失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async updateUnit(req, res) {
    try {
      const updatedUnit = await unitModel.updateUnit(req.params.id, req.body);
      res.json(updatedUnit);
    } catch (error) {
      console.error('更新单位失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async deleteUnit(req, res) {
    try {
      await unitModel.deleteUnit(req.params.id);
      res.status(204).end();
    } catch (error) {
      console.error('删除单位失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  // 物料管理
  async getAllMaterials(req, res) {
    try {
      // 解析请求参数
      let { page, pageSize, ...filters } = req.query;
      
      // 限制最大分页大小，防止请求过大导致资源不足
      const maxPageSize = 100;
      if (parseInt(pageSize) > maxPageSize) {
        pageSize = maxPageSize;
      }
      
      // 获取物料数据
      const result = await materialModel.getAllMaterials(page, pageSize, filters);
      
      // 确保响应格式一致，使用items作为数据列表的键名
      const response = {
        items: result.data || [],
        total: result.pagination?.total || 0,
        page: parseInt(page) || 1,
        pageSize: parseInt(pageSize) || 10
      };
      
      res.json(response);
    } catch (error) {
      console.error('获取物料列表失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async getMaterialById(req, res) {
    try {
      const material = await materialModel.getMaterialById(req.params.id);
      if (material) {
        res.json(material);
      } else {
        res.status(404).json({ message: '物料不存在' });
      }
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  },

  // 批量获取物料信息
  async getMaterialsByIds(req, res) {
    try {
      const { ids } = req.body;

      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        return res.status(400).json({ message: '请提供有效的物料ID数组' });
      }

      // 限制批量查询的数量，防止查询过大
      if (ids.length > 100) {
        return res.status(400).json({ message: '批量查询数量不能超过100个' });
      }

      // 构建查询语句
      const placeholders = ids.map(() => '?').join(',');
      const query = `
        SELECT
          m.*,
          c.name as category_name,
          u.name as unit_name,
          ms.name as material_source_name
        FROM materials m
        LEFT JOIN categories c ON m.category_id = c.id
        LEFT JOIN units u ON m.unit_id = u.id
        LEFT JOIN material_sources ms ON m.material_source_id = ms.id
        WHERE m.id IN (${placeholders})
      `;

      const [materials] = await pool.query(query, ids);

      res.json({
        success: true,
        data: materials,
        total: materials.length
      });
    } catch (error) {
      console.error('批量获取物料失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async createMaterial(req, res) {
    try {
      const newMaterial = await materialModel.createMaterial(req.body);
      res.status(201).json(newMaterial);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  },

  async updateMaterial(req, res) {
    try {
      const updatedMaterial = await materialModel.updateMaterial(req.params.id, req.body);
      res.json(updatedMaterial);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  },

  // 获取下一个物料编码序号
  async getNextMaterialCode(req, res) {
    try {
      const { prefix } = req.query;
      if (!prefix) {
        return res.status(400).json({ message: '编码前缀不能为空' });
      }

      const nextSequence = await materialModel.getNextMaterialSequence(prefix);
      res.json({
        success: true,
        nextSequence: nextSequence,
        prefix: prefix,
        fullCode: prefix + nextSequence.toString().padStart(3, '0')
      });
    } catch (error) {
      console.error('获取下一个物料编码失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async deleteMaterial(req, res) {
    try {
      await materialModel.deleteMaterial(req.params.id);
      res.status(204).end();
    } catch (error) {
      // 如果是业务逻辑错误（如有关联数据不能删除），使用400状态码
      if (error.message.includes('不能删除') || error.message.includes('不可删除')) {
        res.status(400).json({ message: error.message });
      } else {
        // 其他系统错误使用500状态码
        res.status(500).json({ message: error.message });
      }
    }
  },

  // 导入物料数据
  async importMaterials(req, res) {
    try {
      if (!req.body || !req.body.materials || !Array.isArray(req.body.materials)) {
        return res.status(400).json({ message: '无效的请求数据，需要提供物料数组' });
      }

      const result = await materialModel.importMaterials(req.body.materials);
      res.status(200).json(result);
    } catch (error) {
      console.error('导入物料失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  // 导出物料数据
  async exportMaterials(req, res) {
    try {
      const filters = req.body;
      const materials = await materialModel.exportMaterials(filters);
      
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', 'attachment; filename=materials.xlsx');
      
      res.send(materials);
    } catch (error) {
      console.error('导出物料失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  // BOM管理
  async getAllBoms(req, res) {
    try {
      const { page = 1, pageSize = 10, product_id, status, ...filters } = req.query;
      
      if (product_id) {
        // 如果提供了product_id，直接查找最新版本的BOM
        
        // 首先验证该ID是否对应的是产品
        const [productCheck] = await pool.query(
          'SELECT m.id, m.name, m.code, m.specs as product_specs, c.name as category_name FROM materials m LEFT JOIN categories c ON m.category_id = c.id WHERE m.id = ?',
          [product_id]
        );
        
        if (productCheck.length === 0) {
          return res.status(404).json({
            message: '未找到该物料',
            data: []
          });
        }
        
        // 由于数据库中没有明确标记产品的字段，我们不再做类型检查
        // 如果物料存在且有BOM，我们就认为它是一个产品
        
        // 构建过滤条件
        const bomFilters = { ...filters };
        if (status) {
          bomFilters.status = status;
        }

        const bomResult = await bomModel.getLatestBomByProductId(product_id, status);
        
        if (!bomResult || !bomResult.data || bomResult.data.length === 0) {
          return res.status(404).json({ 
            message: '未找到相关的BOM信息',
            data: []
          });
        }
        
        // 直接返回 bomResult，保持与 model 层返回格式一致
        res.json(bomResult);
      } else {
        // 否则，使用原来的分页逻辑
        const result = await bomModel.getAllBoms(page, pageSize, { ...filters, status });
        
        res.json({
          data: result.data || [],
          pagination: result.pagination || {
            total: result.pagination?.total || 0,
            page: parseInt(page),
            pageSize: parseInt(pageSize)
          }
        });
      }
    } catch (error) {
      console.error('获取BOM信息失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async getBomById(req, res) {
    try {
      
      // 获取BOM主表信息
      const [bomRows] = await pool.query(`
        SELECT bm.*, m.name as product_name, m.code as product_code, m.specs as product_specs
        FROM bom_masters bm
        LEFT JOIN materials m ON bm.product_id = m.id
        WHERE bm.id = ?
      `, [req.params.id]);

      if (bomRows && bomRows.length > 0) {
        const bom = bomRows[0];
        
        // 获取BOM明细
        const [detailRows] = await pool.query(`
          SELECT 
            bd.*,
            m.name as material_name,
            m.code as material_code,
            m.specs as material_specs,
            u.name as unit_name
          FROM bom_details bd
          LEFT JOIN materials m ON bd.material_id = m.id
          LEFT JOIN units u ON bd.unit_id = u.id
          WHERE bd.bom_id = ?
        `, [req.params.id]);

        // 检查bom对象是否包含attachment字段，如果不存在则尝试添加字段
        if (!('attachment' in bom)) {
          try {
            await pool.query(`
              ALTER TABLE bom_masters
              ADD COLUMN attachment VARCHAR(255) DEFAULT NULL
            `);
          } catch (err) {
            // 如果字段已存在会报错，但我们可以忽略
            if (err.code !== 'ER_DUP_FIELDNAME') {
              console.error('添加attachment字段失败:', err);
            }
          }
        }

        // 格式化BOM数据，确保字段类型正确
        const formattedBom = {
          id: bom.id,
          product_id: bom.product_id,
          version: bom.version,
          status: typeof bom.status === 'boolean' ? bom.status : Number(bom.status),
          approved: typeof bom.status === 'boolean' ? bom.status : (Number(bom.status) === 2),
          remark: bom.remark || '',
          attachment: bom.attachment || null, // 确保返回attachment字段，即使可能为null
          product_code: bom.product_code,
          product_name: bom.product_name,
          product_specs: bom.product_specs || '',
          created_at: bom.created_at,
          updated_at: bom.updated_at,
          created_by: bom.created_by || '',
          updated_by: bom.updated_by || '',
          approved_by: bom.approved_by || null,
          approved_at: bom.approved_at || null,
          details: detailRows.map(detail => ({
            id: detail.id,
            bom_id: detail.bom_id,
            material_id: detail.material_id,
            quantity: Number(detail.quantity),
            unit_id: detail.unit_id,
            remark: detail.remark || '',
            material_code: detail.material_code,
            material_name: detail.material_name,
            material_specs: detail.material_specs || '',
            specs: detail.material_specs || '',
            unit_name: detail.unit_name
          }))
        };

        // 返回格式化后的BOM信息
        res.json({
          data: formattedBom
        });
      } else {
        res.status(404).json({ message: 'BOM不存在' });
      }
    } catch (error) {
      console.error('获取BOM详情失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async createBom(req, res) {
    try {
      // 提取数据
      const bomData = req.body.bomData;
      const details = req.body.details;
      
      // 验证bomData
      if (!bomData) {
        return res.status(400).json({ message: 'bomData是必需的' });
      }

      // 验证details
      if (!details || !Array.isArray(details) || details.length === 0) {
        return res.status(400).json({ message: 'details是必需的，且不能为空数组' });
      }

      // 验证必要的数据
      if (!bomData.product_id) {
        return res.status(400).json({ message: '产品ID是必需的' });
      }

      if (!bomData.version) {
        return res.status(400).json({ message: '版本号是必需的' });
      }
      
      // 如果提供了status，确保将其转换为数字
      if (bomData.status !== undefined) {
        bomData.status = Number(bomData.status);
      }
      
      // 验证明细数据
      for (const detail of details) {
        if (!detail.material_id) {
          return res.status(400).json({ message: '明细中的物料ID是必需的' });
        }

        if (!detail.quantity) {
          return res.status(400).json({ message: '明细中的数量是必需的' });
        }

        if (!detail.unit_id) {
          return res.status(400).json({ message: '明细中的单位ID是必需的' });
        }
      }

      const newBom = await bomModel.createBom(bomData, details);
      res.status(201).json(newBom);
    } catch (error) {
      console.error('创建BOM失败:', error);
      res.status(500).json({ message: error.message || '创建BOM失败' });
    }
  },

  async updateBom(req, res) {
    try {
      const id = req.params.id;
      
      // 解析请求数据
      let bomData, details;
      
      // 判断是否为multipart/form-data请求
      if (req.is('multipart/form-data') && req.files && req.files.attachment) {
        // 处理表单数据和文件
        if (req.body.bomData) {
          bomData = JSON.parse(req.body.bomData);
        }
        
        if (req.body.details) {
          details = JSON.parse(req.body.details);
        }
        
        // 获取上传的文件
        const attachment = req.files.attachment;
        
        // 如果有上传文件，移动到指定目录
        if (attachment) {
          const uploadDir = path.join(__dirname, '../../uploads/boms');
          
          // 确保目录存在
          if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
          }
          
          // 生成唯一文件名
          const timestamp = Date.now();
          const ext = path.extname(attachment.name);
          const fileName = `bom_${timestamp}${ext}`;
          const filePath = path.join(uploadDir, fileName);
          
          // 移动文件
          await new Promise((resolve, reject) => {
            attachment.mv(filePath, (err) => {
              if (err) return reject(err);
              resolve();
            });
          });
          
          // 将文件路径保存到bomData中
          bomData.attachment = `/uploads/boms/${fileName}`;
        }
      } else {
        // 常规JSON请求
        bomData = req.body.bomData;
        details = req.body.details;
      }
      
      const updatedBom = await bomModel.updateBom(id, bomData, details);
      res.json(updatedBom);
    } catch (error) {
      console.error('更新BOM失败:', error);
      res.status(500).json({ message: error.message || '更新BOM失败' });
    }
  },

  async deleteBom(req, res) {
    try {
      await bomModel.deleteBom(req.params.id);
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  },

  // 获取用户真实姓名的辅助函数
  async getUserRealName(req) {
    // 获取当前用户信息
    const userId = req.user?.id || null;
    console.log('用户对象:', req.user);
    
    // 从数据库获取用户真实姓名
    let userName = '系统用户';
    if (userId) {
      try {
        // 查询用户表获取真实姓名，使用real_name字段
        const [userRows] = await pool.query(`
          SELECT real_name FROM users WHERE id = ?
        `, [userId]);
        
        console.log('数据库中的用户信息:', userRows[0]);
        
        if (userRows && userRows.length > 0 && userRows[0].real_name) {
          // 使用数据库中的real_name字段作为用户姓名
          userName = userRows[0].real_name;
          console.log('使用real_name字段作为用户姓名:', userName);
        } else {
          // 如果没有real_name字段或为空，使用username作为备选
          userName = req.user?.username || '系统用户';
          console.log('未找到real_name字段，使用username:', userName);
        }
      } catch (err) {
        console.error('获取用户信息失败:', err);
        // 使用req.user中的username作为备选
        userName = req.user?.username || '系统用户';
      }
    } else {
      // 如果没有userId，使用req.user中的username
      userName = req.user?.username || '系统用户';
    }
    
    return { userId, userName };
  },

  // 审核BOM
  async approveBom(req, res) {
    try {
      const bomId = req.params.id;

      // 获取BOM信息
      const [bomRows] = await pool.query(`
        SELECT * FROM bom_masters WHERE id = ?
      `, [bomId]);

      if (bomRows.length === 0) {
        return res.status(404).json({ message: 'BOM不存在' });
      }

      // 获取用户真实姓名
      const { userId, userName } = await baseDataController.getUserRealName(req);
      
      // 检查数据库表是否有approved_by和approved_at字段
      try {
        // 检查approved_by字段
        await pool.query(`
          SELECT approved_by FROM bom_masters LIMIT 1
        `);
      } catch (error) {
        if (error.code === 'ER_BAD_FIELD_ERROR') {
          console.log('添加approved_by字段');
          await pool.query(`
            ALTER TABLE bom_masters 
            ADD COLUMN approved_by INT NULL,
            ADD COLUMN approved_at DATETIME NULL
          `);
        } else {
          throw error;
        }
      }
      
      // 更新BOM状态为已审核
      const [updateResult] = await pool.query(`
        UPDATE bom_masters
        SET status = true,
            approved_by = ?,
            approved_at = NOW(),
            updated_at = NOW(),
            updated_by = ?
        WHERE id = ?
      `, [userId, userName, bomId]);

      console.log('更新结果:', updateResult);

      // 验证更新是否成功
      const [verifyRows] = await pool.query(`
        SELECT * FROM bom_masters WHERE id = ?
      `, [bomId]);

      // 获取BOM信息
      const bom = bomRows[0];

      // 返回更新后的BOM信息
      res.json({
        message: 'BOM审核成功',
        data: {
          id: bomId,
          status: true,
          approved_by: userId,
          approved_at: new Date(),
          product_id: bom.product_id,
          version: bom.version,
          updated_by: userName
        }
      });
    } catch (error) {
      console.error('审核BOM失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  // 反审核BOM
  async unapproveBom(req, res) {
    try {
      const bomId = req.params.id;
      // 获取BOM信息
      const [bomRows] = await pool.query(`
        SELECT * FROM bom_masters WHERE id = ?
      `, [bomId]);

      if (bomRows.length === 0) {
        return res.status(404).json({ message: 'BOM不存在' });
      }

      // 获取用户真实姓名
      const { userId, userName } = await baseDataController.getUserRealName(req);

      // 更新BOM状态为未审核
      const [updateResult] = await pool.query(`
        UPDATE bom_masters
        SET status = false,
            approved_by = NULL,
            approved_at = NULL,
            updated_at = NOW(),
            updated_by = ?
        WHERE id = ?
      `, [userName, bomId]);

      // 验证更新是否成功
      const [verifyRows] = await pool.query(`
        SELECT * FROM bom_masters WHERE id = ?
      `, [bomId]);

      // 获取BOM信息
      const bom = bomRows[0];

      // 返回更新后的BOM信息
      res.json({
        message: 'BOM反审核成功',
        data: {
          id: bomId,
          status: false,
          approved_by: null,
          approved_at: null,
          product_id: bom.product_id,
          version: bom.version,
          updated_by: userName
        }
      });
    } catch (error) {
      console.error('反审核BOM失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  // 客户管理
  async getAllCustomers(req, res) {
    try {
      const { page, pageSize, limit, ...filters } = req.query;
      // 支持 pageSize 和 limit 两种参数名
      const actualPageSize = parseInt(pageSize) || parseInt(limit) || 10;
      const result = await customerModel.getAllCustomers(parseInt(page) || 1, actualPageSize, filters);

      // 统一返回格式，确保前端能正确解析
      res.json({
        success: true,
        data: {
          list: result.items || [],
          total: result.total || 0,
          page: result.page || 1,
          pageSize: result.pageSize || actualPageSize
        }
      });
    } catch (error) {
      console.error('获取客户列表失败:', error);
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  },

  async getCustomerById(req, res) {
    try {
      const customer = await customerModel.getCustomerById(req.params.id);
      if (customer) {
        res.json(customer);
      } else {
        res.status(404).json({ message: '客户不存在' });
      }
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  },

  async createCustomer(req, res) {
    try {
      // 检查必填字段
      const { name } = req.body;
      if (!name) {
        return res.status(400).json({ message: '客户名称为必填项' });
      }

      const newCustomer = await customerModel.createCustomer(req.body);
      res.status(201).json(newCustomer);
    } catch (error) {
      console.error('创建客户失败:', error);
      res.status(500).json({ message: error.message || '创建客户失败' });
    }
  },

  async updateCustomer(req, res) {
    try {
      const customerId = req.params.id;
      const updateData = req.body;
      
      // 检查客户是否存在
      const existingCustomer = await customerModel.getCustomerById(customerId);
      if (!existingCustomer) {
        return res.status(404).json({ message: '客户不存在' });
      }

      // 验证更新数据
      if (Object.keys(updateData).length === 0) {
        return res.status(400).json({ message: '没有提供要更新的数据' });
      }

      const updatedCustomer = await customerModel.updateCustomer(customerId, updateData);
      
      if (!updatedCustomer) {
        return res.status(500).json({ message: '更新客户失败，请稍后重试' });
      }

      res.json(updatedCustomer);
    } catch (error) {
      console.error('更新客户时发生错误:', error);
      res.status(500).json({ message: '服务器内部错误', error: error.message });
    }
  },

  async deleteCustomer(req, res) {
    try {
      await customerModel.deleteCustomer(req.params.id);
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  },

  // 供应商管理
  async getAllSuppliers(req, res) {
    try {
      // 直接查询所有供应商，确保获取所有字段
      const [rows] = await pool.query(`
        SELECT
          id,
          code,
          name,
          contact_person,
          contact_phone,
          email,
          address,
          status,
          remark,
          created_at,
          updated_at
        FROM suppliers
        ORDER BY id DESC
      `);

      // 直接返回数组形式的数据
      res.json(rows);
    } catch (error) {
      console.error('获取供应商列表失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async getSupplierById(req, res) {
    try {
      const supplier = await supplierModel.getSupplierById(req.params.id);
      if (supplier) {
        res.json(supplier);
      } else {
        res.status(404).json({ message: '供应商不存在' });
      }
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  },

  async createSupplier(req, res) {
    try {
      const newSupplier = await supplierModel.createSupplier(req.body);
      res.status(201).json(newSupplier);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  },

  async updateSupplier(req, res) {
    try {
      const updatedSupplier = await supplierModel.updateSupplier(req.params.id, req.body);
      res.json(updatedSupplier);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  },

  async deleteSupplier(req, res) {
    try {
      await supplierModel.deleteSupplier(req.params.id);
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  },

  // 获取物料的BOM信息
  async getMaterialBom(req, res) {
    try {
      const materialId = req.params.id;
      const result = await bomModel.getLatestBomByProductId(materialId);
      
      if (result.found) {
        res.json(result.bom);
      } else {
        // 不返回404，而是返回一个表示没有BOM的对象
        res.json({ 
          id: null, 
          product_id: materialId,
          found: false,
          message: '未找到该物料的BOM' 
        });
      }
    } catch (error) {
      console.error('获取物料BOM失败:', error);
      res.status(500).json({ message: error.message || '获取物料BOM失败' });
    }
  },

  // 获取供应商选项
  async getSupplierOptions(req, res) {
    try {
      const [rows] = await pool.query(`
        SELECT id, code, name 
        FROM suppliers 
        WHERE status = 1 
        ORDER BY name
      `);
      
      const options = rows.map(row => ({
        value: row.id,
        label: `${row.code} - ${row.name}`
      }));
      
      res.json(options);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  },

  // 获取物料选项
  async getMaterialOptions(req, res) {
    try {
      const { search = '' } = req.query;
      let whereClause = 'WHERE m.status = 1';
      const params = [];
      
      if (search) {
        whereClause += ' AND (m.name LIKE ? OR m.code LIKE ?)';
        params.push(`%${search}%`, `%${search}%`);
      }
      
      const [rows] = await pool.query(`
        SELECT m.id, m.code, m.name, m.specs, u.name as unit_name
        FROM materials m
        JOIN units u ON m.unit_id = u.id
        ${whereClause}
        ORDER BY m.name
      `, params);
      
      const items = rows.map(row => ({
        id: row.id,
        code: row.code,
        name: row.name,
        specs: row.specs,
        unit_name: row.unit_name,
        value: row.id,
        label: `${row.code} - ${row.name}${row.specs ? ` (${row.specs})` : ''} - ${row.unit_name}`
      }));
      
      res.json({
        items: items,
        total: items.length
      });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  },

  // 获取产品的BOM信息
  async getBomByProductId(req, res) {
    try {
      const productId = req.params.id;
      const { status = 1 } = req.query;

      const result = await bomModel.getLatestBomByProductId(productId, status);

      if (result.data && result.data.length > 0) {
        res.json({
          data: result.data[0]
        });
      } else {
        res.status(404).json({
          message: '未找到该产品的BOM信息',
          data: null
        });
      }
    } catch (error) {
      console.error('获取产品BOM失败:', error);
      res.status(500).json({ message: error.message || '获取产品BOM失败' });
    }
  },

  // 导出BOM数据
  async exportBoms(req, res) {
    try {
      const { productId, version, status, ids } = req.query;

      // 构建查询条件
      let whereClause = 'WHERE 1=1';
      const params = [];

      // 如果提供了ids参数，优先使用ids过滤
      if (ids) {
        const idArray = ids.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
        if (idArray.length > 0) {
          whereClause += ` AND bm.id IN (${idArray.map(() => '?').join(',')})`;
          params.push(...idArray);
        }
      } else {
        // 否则使用其他过滤条件
        if (productId) {
          whereClause += ' AND bm.product_id = ?';
          params.push(productId);
        }

        if (version) {
          whereClause += ' AND bm.version LIKE ?';
          params.push(`%${version}%`);
        }

        if (status !== undefined && status !== '') {
          whereClause += ' AND bm.status = ?';
          params.push(Number(status));
        }
      }

      // 查询BOM数据
      const [bomRows] = await pool.query(`
        SELECT
          bm.id,
          m.code as '产品编码',
          m.name as '产品名称',
          m.specs as '规格型号',
          bm.version as '版本',
          CASE WHEN bm.status = 1 THEN '已审核' ELSE '未审核' END as '状态',
          bm.remark as '备注',
          bm.created_by as '创建人',
          DATE_FORMAT(bm.created_at, '%Y-%m-%d %H:%i:%s') as '创建时间',
          bm.updated_by as '修改人',
          DATE_FORMAT(bm.updated_at, '%Y-%m-%d %H:%i:%s') as '修改时间'
        FROM bom_masters bm
        LEFT JOIN materials m ON bm.product_id = m.id
        ${whereClause}
        ORDER BY bm.created_at DESC
      `, params);

      if (bomRows.length === 0) {
        return res.status(400).json({ message: '没有找到符合条件的BOM数据' });
      }

      // 创建Excel文件
      const xlsx = require('xlsx');
      const ws = xlsx.utils.json_to_sheet(bomRows);
      const wb = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(wb, ws, 'BOM列表');

      // 设置列宽
      const colWidths = [
        { wch: 15 }, // 产品编码
        { wch: 30 }, // 产品名称
        { wch: 20 }, // 规格型号
        { wch: 10 }, // 版本
        { wch: 10 }, // 状态
        { wch: 30 }, // 备注
        { wch: 12 }, // 创建人
        { wch: 20 }, // 创建时间
        { wch: 12 }, // 修改人
        { wch: 20 }  // 修改时间
      ];
      ws['!cols'] = colWidths;

      // 生成Excel缓冲区
      const buf = xlsx.write(wb, { type: 'buffer', bookType: 'xlsx' });

      // 设置响应头
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', 'attachment; filename="bom_list.xlsx"');

      // 发送文件
      res.send(buf);
    } catch (error) {
      console.error('导出BOM失败:', error);
      res.status(500).json({ message: '导出BOM失败', error: error.message });
    }
  },

  // 导入BOM数据
  async importBoms(req, res) {
    try {
      if (!req.file) {
        return res.status(400).json({ message: '请选择要导入的文件' });
      }

      const xlsx = require('xlsx');
      const workbook = xlsx.read(req.file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = xlsx.utils.sheet_to_json(worksheet);

      if (data.length === 0) {
        return res.status(400).json({ message: '文件中没有有效数据' });
      }

      let successCount = 0;
      let errorCount = 0;
      const errors = [];

      for (let i = 0; i < data.length; i++) {
        try {
          const row = data[i];

          // 验证必要字段
          if (!row['产品编码'] || !row['版本']) {
            errors.push(`第${i + 2}行：缺少必要字段（产品编码、版本）`);
            errorCount++;
            continue;
          }

          // 查找产品ID
          const [productRows] = await pool.query(
            'SELECT id FROM materials WHERE code = ?',
            [row['产品编码']]
          );

          if (productRows.length === 0) {
            errors.push(`第${i + 2}行：未找到产品编码 ${row['产品编码']}`);
            errorCount++;
            continue;
          }

          const productId = productRows[0].id;

          // 检查BOM是否已存在
          const [existingBom] = await pool.query(
            'SELECT id FROM bom_masters WHERE product_id = ? AND version = ?',
            [productId, row['版本']]
          );

          if (existingBom.length > 0) {
            errors.push(`第${i + 2}行：产品 ${row['产品编码']} 版本 ${row['版本']} 的BOM已存在`);
            errorCount++;
            continue;
          }

          // 创建BOM
          const bomData = {
            product_id: productId,
            version: row['版本'],
            status: row['状态'] === '已审核' ? 1 : 0,
            remark: row['备注'] || '',
            created_by: '系统导入',
            updated_by: '系统导入'
          };

          await bomModel.createBom(bomData, []); // 暂时创建空的明细，后续可以扩展
          successCount++;
        } catch (error) {
          errors.push(`第${i + 2}行：${error.message}`);
          errorCount++;
        }
      }

      res.json({
        message: `导入完成，成功：${successCount}条，失败：${errorCount}条`,
        successCount,
        errorCount,
        errors: errors.slice(0, 10) // 只返回前10个错误
      });
    } catch (error) {
      console.error('导入BOM失败:', error);
      res.status(500).json({ message: '导入BOM失败', error: error.message });
    }
  },

  // 零部件定位
  async locatePart(req, res) {
    try {
      const partCode = req.params.partCode;

      if (!partCode) {
        return res.status(400).json({ message: '请提供零部件编码' });
      }

      // 查询包含该零部件的所有BOM
      const [results] = await pool.query(`
        SELECT DISTINCT
          bm.id,
          bm.product_id,
          m.code as product_code,
          m.name as product_name,
          bm.version,
          bd.quantity,
          bd_m.code as material_code,
          bd_m.name as material_name,
          u.name as unit_name
        FROM bom_masters bm
        JOIN bom_details bd ON bm.id = bd.bom_id
        JOIN materials bd_m ON bd.material_id = bd_m.id
        JOIN materials m ON bm.product_id = m.id
        LEFT JOIN units u ON bd.unit_id = u.id
        WHERE bd_m.code LIKE ? OR bd_m.code = ?
        ORDER BY bm.created_at DESC
      `, [`%${partCode}%`, partCode]);

      res.json(results);
    } catch (error) {
      console.error('定位零部件失败:', error);
      res.status(500).json({ message: '定位零部件失败', error: error.message });
    }
  },

  // 库位管理
  async getAllLocations(req, res) {
    try {
      const { page = 1, limit = 10, name, code, type, status } = req.query;
      
      // 构建查询参数
      const queryParams = {};
      if (name) queryParams.name = name;
      if (code) queryParams.code = code;
      if (type) queryParams.type = type;
      if (status !== undefined && status !== '') {
        // 处理字符串状态值
        if (status === 'active') {
          queryParams.status = 1;
        } else if (status === 'inactive') {
          queryParams.status = 0;
        } else {
          // 尝试转换为数字
          queryParams.status = Number(status);
        }
      }
      
      const result = await locationsModel.getAll(
        queryParams,
        Number(page),
        Number(limit)
      );
      
      res.json(result);
    } catch (error) {
      console.error('获取库位列表失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async getLocationById(req, res) {
    try {
      const location = await locationsModel.getById(req.params.id);
      if (location) {
        res.json(location);
      } else {
        res.status(404).json({ message: '库位不存在' });
      }
    } catch (error) {
      console.error('获取库位详情失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async createLocation(req, res) {
    try {
      const newLocationId = await locationsModel.create(req.body);
      const newLocation = await locationsModel.getById(newLocationId);
      res.status(201).json(newLocation);
    } catch (error) {
      console.error('创建库位失败:', error);
      // 返回更详细的错误信息
      if (error.message.includes('already exists')) {
        return res.status(400).json({ message: error.message });
      } else if (error.message.includes('Missing required fields')) {
        return res.status(400).json({ message: error.message });
      } else if (error.code === 'ER_DUP_ENTRY') {
        return res.status(400).json({ message: '库位编码已存在，请使用其他编码' });
      }
      res.status(500).json({ 
        message: error.message || '创建库位失败，请稍后重试',
        error: process.env.NODE_ENV === 'development' ? error.stack : undefined
      });
    }
  },

  async updateLocation(req, res) {
    try {
      await locationsModel.update(req.params.id, req.body);
      const updatedLocation = await locationsModel.getById(req.params.id);
      res.json(updatedLocation);
    } catch (error) {
      console.error('更新库位失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async deleteLocation(req, res) {
    try {
      await locationsModel.delete(req.params.id);
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  },

  // 获取仓库列表
  async getWarehouses(req, res) {
    try {
      const locationsController = require('./locationsController');
      await locationsController.getWarehouses(req, res);
    } catch (error) {
      console.error('获取仓库列表失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  // 工序模板相关方法
  // 获取所有工序模板
  async getAllProcessTemplates(req, res) {
    try {
      const page = parseInt(req.query.page) || 1;
      const pageSize = parseInt(req.query.pageSize) || 10;
      const productId = req.query.productId || '';
      const name = req.query.name || '';

      // 构建筛选条件
      let conditions = [];
      let params = [];

      if (productId) {
        conditions.push('pt.product_id = ?');
        params.push(productId);
      }

      if (name) {
        conditions.push('pt.name LIKE ?');
        params.push(`%${name}%`);
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
      
      // 计算总数
      const countSql = `
        SELECT COUNT(*) as total
        FROM process_templates pt
        ${whereClause}
      `;
      
      const [countResult] = await pool.query(countSql, params);
      const total = countResult[0].total;
      
      // 查询数据
      const offset = (page - 1) * pageSize;
      const sql = `
        SELECT 
          pt.*,
          m.code as product_code,
          m.name as product_name
        FROM process_templates pt
        LEFT JOIN materials m ON pt.product_id = m.id
        ${whereClause}
        ORDER BY pt.id DESC
        LIMIT ? OFFSET ?
      `;
      
      params.push(parseInt(pageSize), offset);
      const [templates] = await pool.query(sql, params);
      
      // 查询每个模板的工序明细
      const templatesWithDetails = await Promise.all(templates.map(async (template) => {
        const [processes] = await pool.query(`
          SELECT * FROM process_template_details
          WHERE template_id = ?
          ORDER BY order_num ASC
        `, [template.id]);
        
        return {
          ...template,
          processes
        };
      }));
      
      res.json({
        data: templatesWithDetails,
        pagination: {
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize)
        }
      });
    } catch (error) {
      console.error('获取工序模板列表失败:', error);
      res.status(500).json({ error: '获取工序模板列表失败' });
    }
  },

  // 获取工序模板详情
  async getProcessTemplateById(req, res) {
    try {
      const { id } = req.params;
      
      // 查询模板基本信息
      const [templates] = await pool.query(`
        SELECT 
          pt.*,
          m.code as product_code,
          m.name as product_name
        FROM process_templates pt
        LEFT JOIN materials m ON pt.product_id = m.id
        WHERE pt.id = ?
      `, [id]);
      
      if (templates.length === 0) {
        return res.status(404).json({ error: '工序模板不存在' });
      }
      
      const template = templates[0];
      
      // 查询模板工序明细
      const [processes] = await pool.query(`
        SELECT * FROM process_template_details
        WHERE template_id = ?
        ORDER BY order_num ASC
      `, [id]);
      
      res.json({
        ...template,
        processes
      });
    } catch (error) {
      console.error('获取工序模板详情失败:', error);
      res.status(500).json({ error: '获取工序模板详情失败' });
    }
  },

  // 根据产品ID获取工序模板
  async getProcessTemplateByProductId(req, res) {
    try {
      const { id } = req.params;
      
      // 查询该产品关联的工序模板
      const [templates] = await pool.query(`
        SELECT 
          pt.*,
          m.code as product_code,
          m.name as product_name
        FROM process_templates pt
        LEFT JOIN materials m ON pt.product_id = m.id
        WHERE pt.product_id = ? AND pt.status = 1
        ORDER BY pt.id DESC
        LIMIT 1
      `, [id]);
      
      if (templates.length === 0) {
        return res.json({ data: null });
      }
      
      const template = templates[0];
      
      // 查询模板工序明细
      const [processes] = await pool.query(`
        SELECT * FROM process_template_details
        WHERE template_id = ?
        ORDER BY order_num ASC
      `, [template.id]);
      
      res.json({
        data: {
          ...template,
          processes
        }
      });
    } catch (error) {
      console.error('获取产品工序模板失败:', error);
      res.status(500).json({ error: '获取产品工序模板失败' });
    }
  },

  // 创建工序模板
  async createProcessTemplate(req, res) {
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();
      
      const { name, product_id, description, processes } = req.body;
      
      // 生成模板编码
      const prefix = 'PT';
      const [latestCode] = await connection.query(`
        SELECT code FROM process_templates
        WHERE code LIKE '${prefix}%'
        ORDER BY id DESC
        LIMIT 1
      `);
      
      let code;
      if (latestCode.length === 0) {
        code = `${prefix}001`;
      } else {
        const lastCode = latestCode[0].code;
        const lastNumber = parseInt(lastCode.substring(prefix.length));
        code = `${prefix}${(lastNumber + 1).toString().padStart(3, '0')}`;
      }
      
      // 插入模板主表
      const [result] = await connection.query(`
        INSERT INTO process_templates (code, name, product_id, description, status)
        VALUES (?, ?, ?, ?, 1)
      `, [code, name, product_id, description]);
      
      const templateId = result.insertId;
      
      // 插入工序明细
      if (processes && processes.length > 0) {
        const processValues = processes.map((process, index) => [
          templateId,
          process.order,
          process.name,
          process.description || '',
          process.standard_hours || 0,
          process.department || '',
          process.remark || ''
        ]);
        
        const placeholders = processes.map(() => '(?, ?, ?, ?, ?, ?, ?)').join(', ');
        
        await connection.query(`
          INSERT INTO process_template_details 
          (template_id, order_num, name, description, standard_hours, department, remark)
          VALUES ${placeholders}
        `, processValues.flat());
      }
      
      await connection.commit();
      
      res.status(201).json({
        id: templateId,
        code,
        message: '工序模板创建成功'
      });
    } catch (error) {
      await connection.rollback();
      console.error('创建工序模板失败:', error);
      res.status(500).json({ error: '创建工序模板失败' });
    } finally {
      connection.release();
    }
  },

  // 更新工序模板
  async updateProcessTemplate(req, res) {
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();
      
      const { id } = req.params;
      const { name, product_id, description, processes, status } = req.body;
      
      // 检查模板是否存在
      const [existingTemplate] = await connection.query(`
        SELECT * FROM process_templates WHERE id = ?
      `, [id]);
      
      if (existingTemplate.length === 0) {
        return res.status(404).json({ error: '工序模板不存在' });
      }
      
      // 更新模板主表
      await connection.query(`
        UPDATE process_templates
        SET name = ?, product_id = ?, description = ?, status = ?
        WHERE id = ?
      `, [name, product_id, description, status !== undefined ? status : existingTemplate[0].status, id]);
      
      // 删除原有工序明细
      await connection.query(`
        DELETE FROM process_template_details
        WHERE template_id = ?
      `, [id]);
      
      // 插入新的工序明细
      if (processes && processes.length > 0) {
        const processValues = processes.map((process) => [
          id,
          process.order,
          process.name,
          process.description || '',
          process.standard_hours || 0,
          process.department || '',
          process.remark || ''
        ]);
        
        const placeholders = processes.map(() => '(?, ?, ?, ?, ?, ?, ?)').join(', ');
        
        await connection.query(`
          INSERT INTO process_template_details 
          (template_id, order_num, name, description, standard_hours, department, remark)
          VALUES ${placeholders}
        `, processValues.flat());
      }
      
      await connection.commit();
      
      res.json({ message: '工序模板更新成功' });
    } catch (error) {
      await connection.rollback();
      console.error('更新工序模板失败:', error);
      res.status(500).json({ error: '更新工序模板失败' });
    } finally {
      connection.release();
    }
  },

  // 更新工序模板状态
  async updateProcessTemplateStatus(req, res) {
    try {
      const { id } = req.params;
      const { status } = req.body;
      
      // 检查模板是否存在
      const [existingTemplate] = await pool.query(`
        SELECT * FROM process_templates WHERE id = ?
      `, [id]);
      
      if (existingTemplate.length === 0) {
        return res.status(404).json({ error: '工序模板不存在' });
      }
      
      // 更新状态
      await pool.query(`
        UPDATE process_templates
        SET status = ?
        WHERE id = ?
      `, [status, id]);
      
      res.json({ message: '工序模板状态更新成功' });
    } catch (error) {
      console.error('更新工序模板状态失败:', error);
      res.status(500).json({ error: '更新工序模板状态失败' });
    }
  },

  // 删除工序模板
  async deleteProcessTemplate(req, res) {
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();
      
      const { id } = req.params;
      
      // 检查模板是否存在
      const [existingTemplate] = await connection.query(`
        SELECT * FROM process_templates WHERE id = ?
      `, [id]);
      
      if (existingTemplate.length === 0) {
        return res.status(404).json({ error: '工序模板不存在' });
      }
      
      // 删除工序明细
      await connection.query(`
        DELETE FROM process_template_details
        WHERE template_id = ?
      `, [id]);
      
      // 删除模板主表
      await connection.query(`
        DELETE FROM process_templates
        WHERE id = ?
      `, [id]);
      
      await connection.commit();
      
      res.json({ message: '工序模板删除成功' });
    } catch (error) {
      await connection.rollback();
      console.error('删除工序模板失败:', error);
      res.status(500).json({ error: '删除工序模板失败' });
    } finally {
      connection.release();
    }
  },
  
  // 上传文件处理
  async uploadFile(req, res) {
    try {
      if (!req.file) {
        return res.status(400).json({ 
          success: false,
          message: '没有上传文件' 
        });
      }

      // 生成相对URL路径
      const fileUrl = `/uploads/${req.file.filename}`;
      
      return res.status(200).json({ 
        success: true,
        fileUrl 
      });
    } catch (error) {
      console.error('文件上传失败:', error);
      return res.status(500).json({
        success: false,
        message: '文件上传失败: ' + error.message
      });
    }
  },

  // 物料来源管理
  async getAllMaterialSources(req, res) {
    try {
      const { page = 1, limit = 20, name, code, status } = req.query;

      // 构建查询条件
      let whereConditions = [];
      let queryParams = [];

      if (name) {
        whereConditions.push('name LIKE ?');
        queryParams.push(`%${name}%`);
      }

      if (code) {
        whereConditions.push('code LIKE ?');
        queryParams.push(`%${code}%`);
      }

      if (status !== undefined) {
        whereConditions.push('status = ?');
        queryParams.push(parseInt(status));
      }

      const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

      // 查询总数
      const countQuery = `SELECT COUNT(*) as total FROM material_sources ${whereClause}`;
      const [countResult] = await pool.query(countQuery, queryParams);
      const total = countResult[0].total;

      // 查询数据
      const offset = (page - 1) * limit;
      const dataQuery = `
        SELECT id, name, code, type, sort, status, description, created_at, updated_at
        FROM material_sources
        ${whereClause}
        ORDER BY sort ASC, id ASC
        LIMIT ${parseInt(limit)} OFFSET ${offset}
      `;
      const [dataResult] = await pool.query(dataQuery, queryParams);

      res.json({
        items: dataResult || [],
        total: parseInt(total),
        page: parseInt(page),
        limit: parseInt(limit)
      });
    } catch (error) {
      console.error('获取物料来源列表失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async getMaterialSourceStatistics(req, res) {
    try {
      const statsQuery = `
        SELECT
          COUNT(*) as total,
          SUM(CASE WHEN type = 'internal' THEN 1 ELSE 0 END) as internal,
          SUM(CASE WHEN type = 'external' THEN 1 ELSE 0 END) as external,
          SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active,
          SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as inactive
        FROM material_sources
      `;

      const [result] = await pool.query(statsQuery);
      const stats = result[0];

      res.json({
        data: {
          total: parseInt(stats.total),
          internal: parseInt(stats.internal),
          external: parseInt(stats.external),
          active: parseInt(stats.active),
          inactive: parseInt(stats.inactive)
        }
      });
    } catch (error) {
      console.error('获取物料来源统计失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async getMaterialSourceById(req, res) {
    try {
      const { id } = req.params;
      const query = 'SELECT * FROM material_sources WHERE id = ?';
      const [result] = await pool.query(query, [id]);

      if (result.length === 0) {
        return res.status(404).json({ message: '物料来源不存在' });
      }

      res.json(result[0]);
    } catch (error) {
      console.error('获取物料来源详情失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async createMaterialSource(req, res) {
    try {
      const { name, code, type, sort, status, description } = req.body;

      // 检查编码是否已存在
      const checkQuery = 'SELECT id FROM material_sources WHERE code = ?';
      const [checkResult] = await pool.query(checkQuery, [code]);

      if (checkResult.length > 0) {
        return res.status(400).json({ message: '来源编码已存在' });
      }

      const insertQuery = `
        INSERT INTO material_sources (name, code, type, sort, status, description)
        VALUES (?, ?, ?, ?, ?, ?)
      `;

      const [result] = await pool.query(insertQuery, [name, code, type, sort || 0, status || 1, description]);

      res.status(201).json({
        id: result.insertId,
        name,
        code,
        type,
        sort: sort || 0,
        status: status || 1,
        description,
        message: '创建成功'
      });
    } catch (error) {
      console.error('创建物料来源失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async updateMaterialSource(req, res) {
    try {
      const { id } = req.params;
      const { name, code, type, sort, status, description } = req.body;

      // 检查记录是否存在
      const checkQuery = 'SELECT id FROM material_sources WHERE id = ?';
      const [checkResult] = await pool.query(checkQuery, [id]);

      if (checkResult.length === 0) {
        return res.status(404).json({ message: '物料来源不存在' });
      }

      // 检查编码是否被其他记录使用
      const codeCheckQuery = 'SELECT id FROM material_sources WHERE code = ? AND id != ?';
      const [codeCheckResult] = await pool.query(codeCheckQuery, [code, id]);

      if (codeCheckResult.length > 0) {
        return res.status(400).json({ message: '来源编码已被其他记录使用' });
      }

      const updateQuery = `
        UPDATE material_sources
        SET name = ?, code = ?, type = ?, sort = ?, status = ?, description = ?
        WHERE id = ?
      `;

      await pool.query(updateQuery, [name, code, type, sort, status, description, id]);

      res.json({
        id: parseInt(id),
        name,
        code,
        type,
        sort,
        status,
        description,
        message: '更新成功'
      });
    } catch (error) {
      console.error('更新物料来源失败:', error);
      res.status(500).json({ message: error.message });
    }
  },

  async deleteMaterialSource(req, res) {
    try {
      const { id } = req.params;

      // 检查记录是否存在
      const checkQuery = 'SELECT id FROM material_sources WHERE id = ?';
      const [checkResult] = await pool.query(checkQuery, [id]);

      if (checkResult.length === 0) {
        return res.status(404).json({ message: '物料来源不存在' });
      }

      // 这里可以添加检查是否被其他表引用的逻辑
      // 例如检查是否有物料使用了这个来源

      const deleteQuery = 'DELETE FROM material_sources WHERE id = ?';
      await pool.query(deleteQuery, [id]);

      res.json({ message: '删除成功', id: parseInt(id) });
    } catch (error) {
      console.error('删除物料来源失败:', error);
      res.status(500).json({ message: error.message });
    }
  }
};

module.exports = baseDataController;