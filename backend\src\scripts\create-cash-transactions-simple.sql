CREATE TABLE IF NOT EXISTS cash_transactions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  transaction_type ENUM('income', 'expense') NOT NULL,
  transaction_date DATE NOT NULL,
  amount DECIMAL(15,2) NOT NULL,
  category VARCHAR(50) NOT NULL,
  counterparty VARCHAR(200),
  description TEXT NOT NULL,
  reference_number VARCHAR(100),
  transaction_number VARCHAR(100) UNIQUE NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_transaction_date (transaction_date),
  INDEX idx_transaction_type (transaction_type),
  INDEX idx_category (category),
  INDEX idx_transaction_number (transaction_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO cash_transactions (
  transaction_type, transaction_date, amount, category, counterparty, 
  description, reference_number, transaction_number
) VALUES 
('income', '2024-01-15', 5000.00, 'sales', 'Customer A', 'Sales income', 'SH001', 'CASH1705123456001'),
('expense', '2024-01-16', 300.00, 'office', 'Office Store', 'Office supplies', 'BG001', 'CASH1705123456002'),
('income', '2024-01-17', 2000.00, 'other_income', 'Refund', 'Supplier refund', 'TK001', 'CASH1705123456003'),
('expense', '2024-01-18', 150.00, 'meal', 'Restaurant', 'Team dinner', 'CY001', 'CASH1705123456004'),
('expense', '2024-01-19', 800.00, 'travel', 'Travel Agency', 'Business trip', 'CC001', 'CASH1705123456005'),
('income', '2024-01-20', 3500.00, 'sales', 'Customer B', 'Cash sales', 'SH002', 'CASH1705123456006'),
('expense', '2024-01-21', 200.00, 'office', 'Stationery Store', 'Stationery', 'BG002', 'CASH1705123456007'),
('expense', '2024-01-22', 450.00, 'other_expense', 'Repair Company', 'Equipment repair', 'WX001', 'CASH1705123456008'),
('income', '2024-01-23', 1200.00, 'other_income', 'Interest', 'Bank interest', 'LX001', 'CASH1705123456009'),
('expense', '2024-01-24', 600.00, 'travel', 'Hotel', 'Accommodation', 'ZS001', 'CASH1705123456010');
