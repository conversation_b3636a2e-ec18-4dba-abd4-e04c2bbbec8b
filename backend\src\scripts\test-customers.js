const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: '*********',
  port: 3306,
  user: 'root',
  password: 'mysql_ycMQCy',
  database: 'mes'
};

async function testCustomers() {
  let connection;
  
  try {
    console.log('连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    
    // 检查customers表是否存在
    console.log('\n1. 检查customers表结构:');
    const [tableInfo] = await connection.execute('DESCRIBE customers');
    console.log('表结构:', tableInfo);
    
    // 检查customers表中的数据
    console.log('\n2. 检查customers表数据:');
    const [customers] = await connection.execute('SELECT COUNT(*) as count FROM customers');
    console.log('客户总数:', customers[0].count);
    
    if (customers[0].count > 0) {
      console.log('\n3. 显示前5条客户数据:');
      const [sampleData] = await connection.execute('SELECT * FROM customers LIMIT 5');
      console.log('示例数据:', sampleData);
    } else {
      console.log('\n3. 客户表为空，添加测试数据...');
      
      // 添加一些测试客户数据
      const testCustomers = [
        {
          name: '测试客户A',
          contact_person: '张三',
          contact_phone: '13800138001',
          email: '<EMAIL>',
          address: '北京市朝阳区',
          status: 'active',
          credit_limit: 100000,
          remark: '测试客户A'
        },
        {
          name: '测试客户B',
          contact_person: '李四',
          contact_phone: '13800138002',
          email: '<EMAIL>',
          address: '上海市浦东新区',
          status: 'active',
          credit_limit: 200000,
          remark: '测试客户B'
        },
        {
          name: '测试客户C',
          contact_person: '王五',
          contact_phone: '13800138003',
          email: '<EMAIL>',
          address: '广州市天河区',
          status: 'inactive',
          credit_limit: 50000,
          remark: '测试客户C'
        }
      ];
      
      for (const customer of testCustomers) {
        await connection.execute(`
          INSERT INTO customers 
          (name, contact_person, contact_phone, email, address, status, credit_limit, remark, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
          customer.name,
          customer.contact_person,
          customer.contact_phone,
          customer.email,
          customer.address,
          customer.status,
          customer.credit_limit,
          customer.remark
        ]);
        
        console.log(`已添加客户: ${customer.name}`);
      }
      
      console.log('\n4. 重新检查客户数据:');
      const [newCount] = await connection.execute('SELECT COUNT(*) as count FROM customers');
      console.log('新的客户总数:', newCount[0].count);
      
      const [newData] = await connection.execute('SELECT * FROM customers');
      console.log('所有客户数据:', newData);
    }
    
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n数据库连接已关闭');
    }
  }
}

// 运行测试
testCustomers();
