const express = require('express');
const cors = require('cors');
const { customerModel } = require('../models/baseData');

// 创建一个简单的测试服务器
const app = express();
app.use(cors());
app.use(express.json());

// 测试客户API
app.get('/test/customers', async (req, res) => {
  try {
    console.log('收到客户列表请求，参数:', req.query);
    
    const { page, pageSize, limit, ...filters } = req.query;
    const actualPageSize = parseInt(pageSize) || parseInt(limit) || 10;
    const actualPage = parseInt(page) || 1;
    
    console.log('处理后的参数:', {
      page: actualPage,
      pageSize: actualPageSize,
      filters
    });
    
    const result = await customerModel.getAllCustomers(actualPage, actualPageSize, filters);
    
    console.log('数据库查询结果:', result);
    
    // 统一返回格式，确保前端能正确解析
    const response = {
      success: true,
      data: {
        list: result.items || [],
        total: result.total || 0,
        page: result.page || actualPage,
        pageSize: result.pageSize || actualPageSize
      }
    };
    
    console.log('返回给前端的数据:', response);
    
    res.json(response);
  } catch (error) {
    console.error('获取客户列表失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

const port = 3001;
app.listen(port, () => {
  console.log(`测试API服务器运行在 http://localhost:${port}`);
  console.log('测试URL: http://localhost:3001/test/customers');
});

// 直接测试customerModel
async function testCustomerModel() {
  try {
    console.log('\n=== 直接测试customerModel ===');
    const result = await customerModel.getAllCustomers(1, 10, {});
    console.log('customerModel.getAllCustomers结果:', result);
    console.log('items数量:', result.items?.length);
    console.log('total:', result.total);
  } catch (error) {
    console.error('customerModel测试失败:', error);
  }
}

// 运行测试
testCustomerModel();
