const express = require('express');
const cors = require('cors');

// 创建测试应用
const app = express();
app.use(cors());
app.use(express.json());

// 测试路由
app.get('/test/export', (req, res) => {
  console.log('Export route accessed!');
  res.json({ message: 'Export route is working!' });
});

// 模拟BOM导出路由
app.get('/api/baseData/boms/export', (req, res) => {
  console.log('BOM export route accessed!');
  console.log('Query params:', req.query);
  
  // 模拟Excel文件响应
  res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.setHeader('Content-Disposition', 'attachment; filename=bom_export.xlsx');
  
  // 发送一个简单的响应
  res.send(Buffer.from('Mock Excel Content'));
});

const port = 3001;
app.listen(port, () => {
  console.log(`测试服务器运行在 http://localhost:${port}`);
  console.log('测试URL: http://localhost:3001/api/baseData/boms/export');
});

// 测试主服务器是否运行
const http = require('http');

function testMainServer() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/baseData/boms/export',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    console.log(`主服务器响应状态: ${res.statusCode}`);
    console.log(`响应头:`, res.headers);
  });

  req.on('error', (e) => {
    console.error(`主服务器请求失败: ${e.message}`);
  });

  req.end();
}

// 5秒后测试主服务器
setTimeout(testMainServer, 5000);
