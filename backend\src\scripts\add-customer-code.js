const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: '*********',
  port: 3306,
  user: 'root',
  password: 'mysql_ycMQCy',
  database: 'mes'
};

async function addCustomerCodeColumn() {
  let connection;
  
  try {
    console.log('连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    
    // 检查code字段是否存在
    console.log('检查customers表结构...');
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'mes' 
      AND TABLE_NAME = 'customers' 
      AND COLUMN_NAME = 'code'
    `);
    
    if (columns.length === 0) {
      console.log('code字段不存在，正在添加...');
      
      // 添加code字段
      await connection.execute(`
        ALTER TABLE customers 
        ADD COLUMN code VARCHAR(50) UNIQUE AFTER id
      `);
      
      console.log('code字段添加成功');
      
      // 为现有客户生成编码
      console.log('为现有客户生成编码...');
      const [customers] = await connection.execute('SELECT id, name FROM customers ORDER BY id');
      
      for (let i = 0; i < customers.length; i++) {
        const customer = customers[i];
        const code = `C${String(customer.id).padStart(4, '0')}`;
        
        await connection.execute(
          'UPDATE customers SET code = ? WHERE id = ?',
          [code, customer.id]
        );
        
        console.log(`客户 ${customer.name} 的编码设置为: ${code}`);
      }
      
      console.log('所有客户编码生成完成');
      
    } else {
      console.log('code字段已存在');
      
      // 检查是否有客户没有编码
      const [noCodeCustomers] = await connection.execute(`
        SELECT id, name FROM customers WHERE code IS NULL OR code = ''
      `);
      
      if (noCodeCustomers.length > 0) {
        console.log(`发现 ${noCodeCustomers.length} 个客户没有编码，正在生成...`);
        
        for (const customer of noCodeCustomers) {
          const code = `C${String(customer.id).padStart(4, '0')}`;
          
          await connection.execute(
            'UPDATE customers SET code = ? WHERE id = ?',
            [code, customer.id]
          );
          
          console.log(`客户 ${customer.name} 的编码设置为: ${code}`);
        }
      } else {
        console.log('所有客户都已有编码');
      }
    }
    
    // 显示最终结果
    console.log('\n最终客户列表:');
    const [finalCustomers] = await connection.execute(`
      SELECT id, code, name FROM customers ORDER BY id LIMIT 10
    `);
    
    finalCustomers.forEach(customer => {
      console.log(`ID: ${customer.id}, 编码: ${customer.code}, 名称: ${customer.name}`);
    });
    
  } catch (error) {
    console.error('操作失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n数据库连接已关闭');
    }
  }
}

// 运行脚本
addCustomerCodeColumn();
