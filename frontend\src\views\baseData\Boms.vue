<template>
  <div class="boms-container">
    <div class="page-header">
      <h2>BOM管理</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon> 新增BOM
      </el-button>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="产品">
          <el-select v-model="searchForm.productId" placeholder="请选择产品" clearable filterable>
            <el-option 
              v-for="item in materialOptions" 
              :key="item.id" 
              :label="`${item.code} - ${item.name}`" 
              :value="item.id">
              <span style="float: left">{{ item.code }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.name }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="版本">
          <el-input v-model="searchForm.version" placeholder="请输入版本" clearable></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.approved" placeholder="请选择审核状态" clearable>
            <el-option :value="true" label="已审核"></el-option>
            <el-option :value="false" label="未审核"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" style="margin-right: 10px">
            <el-icon><Search /></el-icon> 查询
          </el-button>
          <el-button @click="resetSearch" style="margin-right: 20px">
            <el-icon><Refresh /></el-icon> 重置
          </el-button>
          <el-dropdown @command="handleMoreCommand">
            <el-button type="primary">
              <el-icon><MoreFilled /></el-icon> 更多
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="toggleSelect">
                  <el-icon><Select /></el-icon> {{ selectionMode ? '取消选择' : '选择' }}
                </el-dropdown-item>
                <el-dropdown-item command="copyBom"><el-icon><CopyDocument /></el-icon> 复制</el-dropdown-item>
                <el-dropdown-item command="replaceBom"><el-icon><Files /></el-icon> 替换</el-dropdown-item>
                <el-dropdown-item command="locatePart"><el-icon><Position /></el-icon> 定位</el-dropdown-item>
                <el-dropdown-item command="exportBom"><el-icon><Download /></el-icon> 导出</el-dropdown-item>
                <el-dropdown-item command="importBom"><el-icon><Upload /></el-icon> 导入</el-dropdown-item>
                <el-dropdown-item command="compareBom"><el-icon><Switch /></el-icon> 版本对比</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计信息 -->
    <div class="statistics-row">
      <el-card class="stat-card" shadow="hover">
        <div class="stat-value">{{ stats.total || 0 }}</div>
        <div class="stat-label">BOM总数</div>
      </el-card>
      <el-card class="stat-card" shadow="hover">
        <div class="stat-value">{{ stats.active || 0 }}</div>
        <div class="stat-label">已审核</div>
      </el-card>
      <el-card class="stat-card" shadow="hover">
        <div class="stat-value">{{ stats.inactive || 0 }}</div>
        <div class="stat-label">未审核</div>
      </el-card>
      <el-card class="stat-card" shadow="hover">
        <div class="stat-value">{{ stats.detailsCount || 0 }}</div>
        <div class="stat-label">物料明细</div>
      </el-card>
      <el-card class="stat-card" shadow="hover">
        <div class="stat-value">{{ formatCurrency(stats.totalCost || 0) }}</div>
        <div class="stat-label">总成本</div>
      </el-card>
    </div>

    <!-- 表格区域 -->
    <el-card class="data-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        :max-height="tableHeight"
        ref="tableRef"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          v-if="selectionMode"
          type="selection"
          width="55"
          :selectable="(row) => true"
        ></el-table-column>
        <el-table-column label="产品编码" width="120">
          <template #default="scope">
            {{ scope.row.product_code || '未知' }}
          </template>
        </el-table-column>
        <el-table-column label="产品名称" width="150">
          <template #default="scope">
            {{ scope.row.product_name || '未知' }}
          </template>
        </el-table-column>
        <el-table-column label="规格型号" width="120">
          <template #default="scope">
            <span v-if="scope.row.product_specs">{{ scope.row.product_specs }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="version" label="版本" width="60"></el-table-column>
        <el-table-column label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.approved)">
              {{ formatStatus(scope.row.approved) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_by" label="创建人" width="100"></el-table-column>
        <el-table-column prop="updated_by" label="修改人" width="100"></el-table-column>
        <el-table-column prop="updated_at" label="修改时间" width="100"></el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="100"></el-table-column>
        <el-table-column prop="remark" label="备注" width="250"></el-table-column>
        <el-table-column label="操作" width="290" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">
              <el-icon><View /></el-icon> 查看
            </el-button>
            <el-button 
              size="small" 
              type="primary" 
              @click="handleEdit(scope.row)" 
              v-if="!scope.row.approved">
              <el-icon><Edit /></el-icon> 编辑
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDelete(scope.row)" 
              v-if="!scope.row.approved">
              <el-icon><Delete /></el-icon> 删除
            </el-button>
            <el-button 
              size="small" 
              type="success" 
              @click="handleApprove(scope.row)"
              v-if="!scope.row.approved">
              <el-icon><Check /></el-icon> 审核
            </el-button>
            <el-button 
              size="small" 
              type="warning" 
              @click="handleUnapprove(scope.row)"
              v-if="scope.row.approved">
              <el-icon><Close /></el-icon> 反审
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="Math.max(parseInt(total) || 0, 1)"
          :page-sizes="[10, 20, 50, 100]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="900px"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品" prop="product_id">
              <el-select v-model="form.product_id" placeholder="请选择产品" style="width: 100%" filterable @change="handleProductChange">
                <el-option 
                  v-for="item in materialOptions" 
                  :key="item.id" 
                  :label="`${item.code} - ${item.name}`" 
                  :value="item.id">
                  <div style="display: flex; justify-content: space-between; align-items: center">
                    <span style="font-weight: bold">{{ item.code }}</span>
                    <span style="color: #8492a6; margin-left: 10px">{{ item.name }}</span>
                    <span style="color: #909399; font-size: 12px" v-if="item.specs">{{ item.specs }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="BOM版本" prop="version">
              <el-input
                v-model="form.version"
                placeholder="自动生成版本号"
                readonly
              >
                <template #suffix>
                  <el-tooltip
                    :content="isEdit ? '编辑保存时自动递增版本号' : '新增时自动生成版本号'"
                    placement="top"
                  >
                    <el-icon v-if="isEdit" style="color: #409EFF;"><Refresh /></el-icon>
                    <el-icon v-else style="color: #67C23A;"><Plus /></el-icon>
                  </el-tooltip>
                </template>
              </el-input>
              <div style="font-size: 12px; color: #909399; margin-top: 4px;">
                {{ isEdit ? '保存时版本号将自动递增' : '版本号将自动生成' }}
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="附件">
          <el-upload
            class="upload-demo"
            action="#"
            :auto-upload="false"
            :on-change="handleAttachmentChange"
            :on-remove="handleAttachmentRemove"
            :file-list="fileList"
            multiple
            :limit="5"
            accept=".jpg,.jpeg,.png,.pdf"
          >
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">
                支持上传图片(jpg/jpeg/png)或PDF文件，大小不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" :rows="2" placeholder="请输入备注"></el-input>
        </el-form-item>
        
        <!-- BOM明细 -->
        <el-divider content-position="left">BOM明细</el-divider>
        
        <div class="bom-details">
          <el-button type="primary" @click="addDetail" style="margin-bottom: 15px">添加明细</el-button>
          
          <el-table :data="form.details" border>
            <el-table-column label="序号" type="index" width="60"></el-table-column>
            <el-table-column label="物料" width="250">
              <template #default="scope">
                <el-select 
                  v-model="scope.row.material_id" 
                  placeholder="请选择物料"
                  style="width: 100%"
                  filterable
                  @change="handleMaterialChange($event, scope.$index)"
                  :filter-method="filterMaterial"
                  remote
                  :remote-method="filterMaterial"
                >
                  <el-option 
                    v-for="item in materialOptions" 
                    :key="item.id" 
                    :label="`${item.code} - ${item.name}`" 
                    :value="item.id">
                    <div style="display: flex; justify-content: space-between; align-items: center">
                      <span style="font-weight: bold">{{ item.code }}</span>
                      <span style="color: #8492a6; margin-left: 10px">{{ item.name }}</span>
                    </div>
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="规格型号" width="120">
              <template #default="scope">
                <div>{{ scope.row.material_specs || '-' }}</div>
              </template>
            </el-table-column>
            <el-table-column label="用量" width="150">
              <template #default="scope">
                <el-input-number 
                  v-model="scope.row.quantity" 
                  :min="0.01" 
                  :precision="2" 
                  :step="0.01" 
                  style="width: 100%">
                </el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="单位" width="100">
              <template #default="scope">
                <el-select v-model="scope.row.unit_id" placeholder="请选择单位" style="width: 100%">
                  <el-option 
                    v-for="item in unitOptions" 
                    :key="item.id" 
                    :label="item.name" 
                    :value="item.id">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="备注" width="200">
              <template #default="scope">
                <el-input v-model="scope.row.remark" placeholder="备注"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="scope">
                <el-button 
                  type="danger" 
                  link
                  class="delete-text-btn"
                  @click="removeDetail(scope.$index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 查看BOM对话框 -->
    <el-dialog
      title="查看BOM详情"
      v-model="viewDialogVisible"
      width="900px"
    >
      <div v-if="currentBom">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="产品编码">{{ currentBom.product_code }}</el-descriptions-item>
          <el-descriptions-item label="产品名称">{{ currentBom.product_name }}</el-descriptions-item>
          <el-descriptions-item label="规格型号">{{ currentBom.product_specs || '-' }}</el-descriptions-item>
          <el-descriptions-item label="BOM版本">{{ currentBom.version }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentBom.approved ? 'success' : 'warning'">
              {{ currentBom.approved ? '已审核' : '未审核' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">
            {{ currentBom.created_by }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentBom.created_at }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentBom.remark }}</el-descriptions-item>
          <el-descriptions-item label="修改人">
            {{ currentBom.updated_by }}
          </el-descriptions-item>
          <el-descriptions-item label="最后修改时间">{{ currentBom.updated_at }}</el-descriptions-item>
        </el-descriptions>
        
        <!-- 显示附件 -->
        <div v-if="currentBom.attachment" class="attachment-section">
          <h3>附件</h3>
          <div class="attachment-preview">
            <el-image 
              v-if="isImageFile(currentBom.attachment)"
              :src="getAttachmentUrl(currentBom.attachment)"
              :preview-src-list="[getAttachmentUrl(currentBom.attachment)]"
              fit="contain"
              style="max-width: 100%; max-height: 300px;"
            />
            <div v-else-if="isPdfFile(currentBom.attachment)" class="pdf-preview">
              <iframe 
                :src="getAttachmentUrl(currentBom.attachment)" 
                width="100%" 
                height="500" 
                style="border: 1px solid #eee; border-radius: 4px;"
              ></iframe>
              <div class="pdf-actions">
                <el-button type="primary" @click="openAttachment(currentBom.attachment)">
                  <el-icon><Document /></el-icon> 在新窗口查看
                </el-button>
                <el-button type="success" @click="downloadAttachment(currentBom.attachment)">
                  <el-icon><Download /></el-icon> 下载文件
                </el-button>
              </div>
            </div>
            <div v-else class="other-file-preview">
              <el-button type="primary" @click="openAttachment(currentBom.attachment)">
                <el-icon><Document /></el-icon> 查看文件
              </el-button>
              <el-button type="success" @click="downloadAttachment(currentBom.attachment)">
                <el-icon><Download /></el-icon> 下载文件
              </el-button>
            </div>
          </div>
        </div>
        
        <el-divider content-position="left">BOM明细</el-divider>
        
        <el-table :data="currentBom.details" border>
          <el-table-column label="序号" type="index" width="50"></el-table-column>
          <el-table-column prop="material_code" label="物料编码" width="120"></el-table-column>
          <el-table-column prop="material_name" label="物料名称" width="200"></el-table-column>
          <el-table-column label="规格型号" width="220">
            <template #default="scope">
              <span>{{ scope.row.specification || scope.row.material_specs || scope.row.specs || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="用量" width="100"></el-table-column>
          <el-table-column prop="unit_name" label="单位" width="80"></el-table-column>
          <el-table-column prop="remark" label="备注" width="200"></el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 导入BOM对话框 -->
    <el-dialog
      title="导入BOM数据"
      v-model="importDialogVisible"
      width="500px"
    >
      <el-form>
        <el-form-item label="选择文件">
          <el-upload
            class="upload-demo"
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :limit="1"
            :file-list="fileList"
            accept=".xlsx,.xls"
          >
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">
                只能上传Excel文件(xlsx/xls)，请确保文件格式正确
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitImport" :disabled="!selectedFile">
            导入
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 复制BOM对话框 -->
    <el-dialog
      title="复制BOM"
      v-model="copyDialogVisible"
      width="600px"
    >
      <el-form :model="copyForm" label-width="120px">
        <el-form-item label="源BOM" prop="bomId">
          <el-select v-model="copyForm.bomId" placeholder="请选择源BOM" style="width: 100%" filterable>
            <el-option 
              v-for="item in bomOptions" 
              :key="item.id" 
              :label="`${item.product_code} - ${item.version}`" 
              :value="item.id">
              <span style="float: left">{{ item.product_code }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">版本: {{ item.version }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="目标产品" prop="targetProductId">
          <el-select v-model="copyForm.targetProductId" placeholder="请选择目标产品" style="width: 100%" filterable>
            <el-option 
              v-for="item in materialOptions" 
              :key="item.id" 
              :label="`${item.code} - ${item.name}`" 
              :value="item.id">
              <span style="float: left">{{ item.code }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.name }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="新版本号">
          <el-input v-model="copyForm.newVersion" placeholder="请输入新版本号"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="copyDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitCopy" :disabled="!copyForm.bomId || !copyForm.targetProductId || !copyForm.newVersion">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 替换BOM对话框 -->
    <el-dialog
      title="替换BOM"
      v-model="replaceDialogVisible"
      width="600px"
    >
      <el-form :model="replaceForm" label-width="120px">
        <el-form-item label="选择源BOM">
          <el-select v-model="replaceForm.sourceBomId" placeholder="请选择源BOM" style="width: 100%" filterable @change="handleSourceBomChange">
            <el-option 
              v-for="item in bomOptions" 
              :key="item.id" 
              :label="`${item.product_code} - ${item.version}`" 
              :value="item.id">
              <span style="float: left">{{ item.product_code }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">版本: {{ item.version }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择目标BOM">
          <el-select v-model="replaceForm.targetBomId" placeholder="请选择目标BOM" style="width: 100%" filterable>
            <el-option 
              v-for="item in bomOptions" 
              :key="item.id" 
              :label="`${item.product_code} - ${item.version}`" 
              :value="item.id">
              <span style="float: left">{{ item.product_code }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">版本: {{ item.version }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="替换方式">
          <el-radio-group v-model="replaceForm.replaceMode">
            <el-radio :value="'all'">完全替换</el-radio>
            <el-radio :value="'add'">追加不存在项</el-radio>
            <el-radio :value="'update'">仅更新已有项</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="replaceDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitReplace" :disabled="!replaceForm.sourceBomId || !replaceForm.targetBomId">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 定位零部件对话框 -->
    <el-dialog
      title="定位零部件"
      v-model="locatePartDialogVisible"
      width="1010px"
    >
      <el-form :model="locateForm" :inline="true">
        <el-form-item label="零部件编码" prop="partCode" style="width: 60%">
          <el-input v-model="locateForm.partCode" placeholder="请输入零部件编码" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchPart" :disabled="!locateForm.partCode" style="margin-right: 10px">
            <el-icon><Search /></el-icon> 查找
          </el-button>
          <el-button type="warning" @click="handleReplaceParts" :disabled="selectedLocateResults.length === 0">
            <el-icon><Files /></el-icon> 批量替换
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 查询结果 -->
      <div v-if="locateResults.length > 0">
        <el-divider content-position="left">查询结果</el-divider>
        <div style="margin-bottom: 10px;">
          <el-checkbox v-model="selectAll" @change="handleSelectAllChange">全选</el-checkbox>
          <span style="margin-left: 10px;">已选择 {{ selectedLocateResults.length }} 项</span>
        </div>
        <el-table :data="locateResults" border style="width: 100%" max-height="400px" @selection-change="handleLocateSelectionChange">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="产品编码" prop="product_code" width="120"></el-table-column>
          <el-table-column label="产品名称" prop="product_name" width="150"></el-table-column>
          <el-table-column label="BOM版本" prop="version" width="100"></el-table-column>
          <el-table-column label="零件编码" width="120">
            <template #default="scope">
              <span style="color: #f56c6c; font-weight: bold">{{ scope.row.material_code }}</span>
            </template>
          </el-table-column>
          <el-table-column label="零件名称" prop="material_name" width="150"></el-table-column>
          <el-table-column label="零件数量" width="100">
            <template #default="scope">
              {{ scope.row.quantity }} {{ scope.row.unit_name }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180">
            <template #default="scope">
              <el-button type="primary" size="small" @click="viewBomDetail(scope.row)">
                <el-icon><View /></el-icon> 详情
              </el-button>
              <el-button type="warning" size="small" @click="handleReplaceSinglePart(scope.row)">
                <el-icon><Files /></el-icon> 替换
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else-if="searchPerformed" class="no-data">
        <el-empty description="未找到包含该零部件的BOM"></el-empty>
      </div>
    </el-dialog>

    <!-- 零部件替换对话框 -->
    <el-dialog
      title="零部件替换"
      v-model="replacePartDialogVisible"
      width="600px"
    >
      <el-form :model="replacePartForm" label-width="120px">
        <el-form-item label="当前零部件">
          <el-input v-model="replacePartForm.currentPartCode" disabled></el-input>
        </el-form-item>
        <el-form-item label="替换为" prop="newPartId">
          <el-select v-model="replacePartForm.newPartId" placeholder="请选择替换零部件" filterable style="width: 100%">
            <el-option 
              v-for="item in materialOptions" 
              :key="item.id" 
              :label="`${item.code} - ${item.name}`" 
              :value="item.id">
              <span style="float: left">{{ item.code }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.name }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保持数量">
          <el-switch v-model="replacePartForm.keepQuantity" />
        </el-form-item>
        <el-form-item label="新数量" v-if="!replacePartForm.keepQuantity">
          <el-input-number v-model="replacePartForm.newQuantity" :min="0.01" :precision="2" :step="0.01" style="width: 100%"></el-input-number>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="replacePartDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitReplacePart" :disabled="!replacePartForm.newPartId">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { baseDataApi, api } from '@/services/api';

import { Plus, Edit, Delete, Search, Refresh, View, Download, MoreFilled, CopyDocument, Files, Upload, Position, Document, Check, Close, Switch, Select } from '@element-plus/icons-vue';

// 数据加载状态
const loading = ref(false);

// 表格高度
const tableHeight = ref('calc(100vh - 350px)');

// 表格数据
const tableData = ref([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);

// 统计数据
const stats = reactive({
  total: 0,
  active: 0,
  inactive: 0,
  detailsCount: 0
});

// 选择相关
const selectionMode = ref(false);
const selectedRows = ref([]);
const tableRef = ref(null);

// 搜索表单
const searchForm = reactive({
  productId: '',
  version: '',
  approved: ''
});

// 新增/编辑表单
const formRef = ref(null);
const form = reactive({
  id: '',
  product_id: '',
  version: '',
  remark: '',
  details: [],
  attachment: null
});

// 表单校验规则
const rules = {
  product_id: [{ required: true, message: '请选择产品', trigger: 'change' }],
  version: [{ required: true, message: '请输入BOM版本', trigger: 'blur' }]
};

// 对话框控制
const dialogVisible = ref(false);
const dialogTitle = ref('新增BOM');
const isEdit = ref(false);
const fileList = ref([]);

// 查看BOM对话框
const viewDialogVisible = ref(false);
const currentBom = ref(null);

// 导入BOM相关数据
const importDialogVisible = ref(false);
const selectedFile = ref(null);

// 复制BOM相关数据
const copyDialogVisible = ref(false);
const copyForm = reactive({
  bomId: null,
  targetProductId: null,
  newVersion: '',
});
const bomOptions = ref([]);

// 替换BOM相关数据
const replaceDialogVisible = ref(false);
const replaceForm = reactive({
  sourceBomId: null,
  targetBomId: null,
  replaceMode: 'all'
});

// 下拉选项
const materialOptions = ref([]);
const unitOptions = ref([]);

// 定位零部件相关数据
const locatePartDialogVisible = ref(false);
const locateForm = reactive({
  partCode: ''
});
const locateResults = ref([]);
const searchPerformed = ref(false);
const selectedLocateResults = ref([]);
const selectAll = ref(false);

// 零部件替换相关数据
const replacePartDialogVisible = ref(false);
const replacePartForm = reactive({
  currentPartCode: '',
  currentBomIds: [],
  newPartId: null,
  keepQuantity: true,
  newQuantity: 1
});

// 初始化
onMounted(async () => {
  // 初始化时加载第一页数据
  currentPage.value = 1;
  pageSize.value = 10;

  await fetchMaterials(); // 先获取物料数据
  await fetchUnits(); // 再获取单位数据
  loadData(); // 最后获取BOM数据
});

// 获取BOM列表
const loadData = async () => {
  try {
    loading.value = true;

    // 确保物料数据已加载
    if (materialOptions.value.length === 0) {
      await fetchMaterials();
    }

    // 转换搜索参数
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      productId: searchForm.productId,
      version: searchForm.version,
      approved: searchForm.approved !== '' ? Number(searchForm.approved) : undefined
    };

    // 移除空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key];
      }
    });

    const response = await baseDataApi.getBoms(params);

    // 处理后端返回的数据格式
    let bomList = [];
    let totalCount = 0;

    if (response.data && response.data.data) {
      bomList = response.data.data;
      totalCount = response.data.pagination?.total || response.data.total || bomList.length;
    } else if (response.data && response.data.list) {
      bomList = response.data.list;
      totalCount = response.data.total || bomList.length;
    } else if (response.data && response.data.items) {
      bomList = response.data.items;
      totalCount = response.data.total || bomList.length;
    } else if (Array.isArray(response.data)) {
      bomList = response.data;
      totalCount = response.data.length;
    }

    // 处理BOM数据
    const processedData = bomList.map(bom => {
      const productId = bom.product_id || bom.productId;
      const product = materialOptions.value.find(m => m.id === productId);
      const status = typeof bom.status === 'boolean' ? bom.status : Boolean(bom.status);

      return {
        ...bom,
        product_id: productId,
        product_code: product?.code || bom.product_code || '未知',
        product_name: product?.name || bom.product_name || '未知',
        product_specs: bom.product_specs || product?.specs || '',
        created_at: bom.created_at ? formatDate(bom.created_at) : '',
        updated_at: bom.updated_at ? formatDate(bom.updated_at) : '',
        created_by: bom.created_by || bom.createdBy || '-',
        updated_by: bom.updated_by || bom.updatedBy || '-',
        status: status,
        approved: status
      };
    });

    // 直接更新数据
    tableData.value = processedData;
    total.value = totalCount;

    // 计算统计数据
    calculateStats();
  } catch (error) {
    console.error('获取BOM列表失败:', error);
    const errorMessage = error.response?.data?.message || '获取BOM列表失败';
    ElMessage.error(errorMessage);
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 获取物料列表
const fetchMaterials = async () => {
  try {
    const response = await baseDataApi.getMaterials({ pageSize: 1000 });

    // 处理后端返回的数据格式
    let materials = [];

    if (response && response.data) {
      if (response.data.items && Array.isArray(response.data.items)) {
        materials = response.data.items;
      } else if (response.data.data && Array.isArray(response.data.data)) {
        materials = response.data.data;
      } else if (response.data.list && Array.isArray(response.data.list)) {
        materials = response.data.list;
      } else if (Array.isArray(response.data)) {
        materials = response.data;
      } else {
        materials = [];
      }
    } else {
      materials = [];
    }
    
    // 保存当前已有的物料选项，避免覆盖临时添加的项
    const existingOptions = materialOptions.value || [];
    
    // 创建新的物料选项列表
    const newOptions = materials
      .filter(item => item && item.id && (item.code || item.name)) // 过滤无效数据
      .map(item => ({
        id: item.id,
        code: item.code || '',
        name: item.name || '',
        unit_id: item.unit_id,
        specs: item.specs || ''
      }));
    
    // 合并新旧选项，确保不重复
    const mergedOptions = [...newOptions];
    
    // 添加可能存在的临时选项（如果在新选项中不存在）
    existingOptions.forEach(existingItem => {
      if (!newOptions.some(newItem => newItem.id === existingItem.id)) {
        mergedOptions.push(existingItem);
      }
    });

    // 更新物料选项
    materialOptions.value = mergedOptions;

    if (materialOptions.value.length === 0) {
      ElMessage.warning('未获取到有效的物料数据，请先在物料管理中添加物料');
    }
  } catch (error) {
    console.error('获取物料列表失败:', error);
    const errorMessage = error.response?.data?.message || '获取物料列表失败';
    ElMessage.error(errorMessage);
    // 保留现有选项，避免清空
    if (!materialOptions.value) {
      materialOptions.value = [];
    }
  }
};

// 获取单位列表
const fetchUnits = async () => {
  try {
    const response = await baseDataApi.getUnits();
    // 处理后端返回的数据格式
    if (response.data && response.data.list) {
      unitOptions.value = response.data.list;
    } else if (response.data && response.data.data) {
      unitOptions.value = response.data.data;
    } else if (Array.isArray(response.data)) {
      unitOptions.value = response.data;
    } else {
      unitOptions.value = [];
    }
  } catch (error) {
    ElMessage.error('获取单位列表失败');
  }
};

// 搜索
const handleSearch = () => {
  currentPage.value = 1;
  loadData();
};

// 重置搜索
const resetSearch = () => {
  searchForm.productId = '';
  searchForm.version = '';
  searchForm.approved = '';
  currentPage.value = 1;
  loadData();
};

// 分页相关
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1; // 重置到第一页
  loadData();
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadData();
};

// 新增BOM
const handleAdd = async () => {
  dialogTitle.value = '新增BOM';
  isEdit.value = false;
  resetForm();

  // 自动设置初始版本
  form.version = generateInitialVersion();

  // 确保物料数据是最新的
  await fetchMaterials();

  if (materialOptions.value.length === 0) {
    ElMessage.warning('未找到可用的物料数据，请先在物料管理中添加物料');
    return;
  }

  dialogVisible.value = true;
};

// 查看BOM
const handleView = async (row) => {
  try {
    // 确保物料数据已加载
    if (materialOptions.value.length === 0) {
      await fetchMaterials();
    }
    
    // 保存列表中的创建人和修改人信息，以便在API返回的详情中没有这些信息时使用
    const fromList_created_by = row.created_by;
    const fromList_updated_by = row.updated_by;
    
    // 获取BOM详情数据
    const response = await baseDataApi.getBom(row.id);
    
    // 初始化BOM数据
    let bomData = null;
    
    // 处理不同的数据结构
    if (response.data) {
      if (response.data.data && typeof response.data.data === 'object') {
        // 数据在data字段中
        bomData = response.data.data;
      } else {
        // 数据直接在response.data中
        bomData = response.data;
      }
    }
    
    if (!bomData) {
      throw new Error('无法获取BOM数据');
    }
    
    // 获取产品信息以补充产品规格型号
    const productId = bomData.product_id;
    const product = materialOptions.value.find(m => m.id === productId);
    
    // 确保details是数组
    if (!bomData.details || !Array.isArray(bomData.details) || bomData.details.length === 0) {
      bomData.details = [];
    } else {
      // 处理明细数据，添加规格型号信息
      bomData.details = bomData.details.map(detail => {
        // 找到对应的物料信息
        const material = materialOptions.value.find(m => m.id === detail.material_id);
        
        return {
          ...detail,
          material_specs: detail.material_specs || detail.specs || material?.specs || '',
          specification: detail.specification || detail.material_specs || detail.specs || material?.specs || ''
        };
      });
    }

    // 在设置currentBom之前，处理创建人和修改人信息
    
    // 首先从API返回的BOM详情中尝试获取
    let createdBy = bomData.created_by || bomData.createdBy || bomData.createBy || bomData.creator;
    let updatedBy = bomData.updated_by || bomData.updatedBy || bomData.updateBy || bomData.modifier;
    
    // 如果API返回中没有这些字段，就使用列表中的信息
    if (!createdBy && fromList_created_by) {
      createdBy = fromList_created_by;
    }
    
    if (!updatedBy && fromList_updated_by) {
      updatedBy = fromList_updated_by;
    }
    
    // 设置当前BOM数据
    currentBom.value = {
      ...bomData,
      // 处理status字段，可能是布尔值或数字
      status: typeof bomData.status === 'boolean' ? bomData.status : Number(bomData.status || 0),
      product_specs: bomData.product_specs || product?.specs || '',
      created_at: bomData.created_at ? new Date(bomData.created_at).toLocaleDateString() : '',
      updated_at: bomData.updated_at ? new Date(bomData.updated_at).toLocaleDateString() : '',
      // 使用处理后的值，保留从后端获取的创建人和修改人
      created_by: createdBy || '-', // 如果找不到创建人，显示"-"
      updated_by: updatedBy || '-',  // 如果找不到修改人，显示"-"
      // 确保审核状态字段存在 - 如果status是布尔值，直接使用它作为approved值
      approved: typeof bomData.status === 'boolean' ? bomData.status : 
                (bomData.status === 2 ? true : 
                (bomData.approved !== undefined ? bomData.approved : 
                (bomData.is_approved || false)))
    };

    viewDialogVisible.value = true;
  } catch (error) {
    ElMessage.error(`获取BOM详情失败: ${error.message}`);
  }
};

// 编辑BOM
const handleEdit = async (row) => {
  // 检查是否已审核
  if (row.approved) {
    ElMessage.warning('已审核的BOM不能编辑');
    return;
  }
  
  dialogTitle.value = '编辑BOM';
  isEdit.value = true;
  
  try {
    // 始终重新加载物料数据，确保有最新数据
    await fetchMaterials();
    
    const response = await baseDataApi.getBom(row.id);
    
    // 获取数据后再重置表单，避免中间状态触发验证
    resetForm();
    
    // 初始化BOM数据
    let bomData = null;
    
    // 处理不同的数据结构
    if (response.data) {
      if (response.data.data && typeof response.data.data === 'object') {
        // 数据在data字段中
        bomData = response.data.data;
      } else {
        // 数据直接在response.data中
        bomData = response.data;
      }
    }
    
    if (!bomData) {
      throw new Error('无法获取BOM数据');
    }
    
    // 设置表单数据
    form.id = bomData.id;
    
    // 处理 product_id，确保它是正确的类型和值
    let productId;
    if (Array.isArray(bomData.product_id)) {
      // 如果是数组，尝试获取第一个元素
      const firstItem = bomData.product_id[0];
      if (firstItem && typeof firstItem === 'object' && firstItem.id) {
        productId = firstItem.id;
      } else {
        productId = firstItem;
      }
    } else {
      productId = bomData.product_id;
    }
    
    // 确保 product_id 是数字类型
    const numericProductId = productId ? Number(productId) : null;
    
    // 查找完整的产品信息
    if (numericProductId) {
      // 查找产品信息
      const product = materialOptions.value.find(item => item.id === numericProductId);
      
      if (product) {
        // 如果找到产品信息，设置产品ID
        form.product_id = product.id;
      } else {
        // 如果没有找到产品信息，可能需要重新加载产品列表
        // 这里可以添加重新加载产品列表的逻辑，或者直接设置ID
        form.product_id = numericProductId;

        // 如果有产品编码和名称，可以临时添加到选项中
        if (bomData.product_code && bomData.product_name) {
          const tempProduct = {
            id: numericProductId,
            code: bomData.product_code,
            name: bomData.product_name,
            specs: bomData.product_specs || ''
          };

          // 检查是否已存在
          const existingProduct = materialOptions.value.find(item => item.id === numericProductId);
          if (!existingProduct) {
            materialOptions.value.push(tempProduct);
          }
        }
      }
    } else {
      form.product_id = '';
    }
    
    // 编辑时自动递增版本号
    form.version = incrementVersion(bomData.version);
    // 处理status字段，可能是布尔值或数字
    form.status = typeof bomData.status === 'boolean' ? bomData.status : Number(bomData.status || 0);
    form.remark = bomData.remark || '';
    
    // 处理明细数据
    if (bomData.details && Array.isArray(bomData.details)) {
      form.details = bomData.details.map(detail => {
        // 找到对应的物料信息以获取最新规格
        const material = materialOptions.value.find(m => m.id === detail.material_id);
        
        return {
          material_id: detail.material_id,
          quantity: Number(detail.quantity),
          unit_id: detail.unit_id,
          remark: detail.remark || '',
          material_code: detail.material_code,
          material_name: detail.material_name,
          material_specs: detail.material_specs || detail.specs || material?.specs || ''
        };
      });
    }

    dialogVisible.value = true;
  } catch (error) {
    ElMessage.error(`获取BOM详情失败: ${error.message}`);
  }
};

// 删除BOM
const handleDelete = (row) => {
  // 检查是否已审核
  if (row.approved) {
    ElMessage.warning('已审核的BOM不能删除');
    return;
  }
  
  ElMessageBox.confirm(`确定要删除 ${row.product_name || '此'} BOM吗？`, '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await baseDataApi.deleteBom(row.id);
      ElMessage.success('删除成功');
      loadData();
    } catch (error) {
      ElMessage.error('删除BOM失败');
    }
  }).catch(() => {});
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }

  // 确保所有字段都被正确初始化为预期的类型
  form.id = '';
  form.product_id = ''; // 字符串，会在提交时转换为数字
  form.version = '';
  form.remark = '';
  form.details = []; // 空数组
  form.attachment = null; // 清空附件
  fileList.value = []; // 清空文件列表
};

// 添加BOM明细
const addDetail = () => {
  form.details.push({
    material_id: '',
    quantity: 1,
    unit_id: '',
    remark: '',
    material_code: '',
    material_name: '',
    material_specs: ''
  });
};

// 删除BOM明细
const removeDetail = (index) => {
  form.details.splice(index, 1);
};

// 物料选择变更时，自动设置对应的单位
const handleMaterialChange = (materialId, index) => {
  const material = materialOptions.value.find(item => item.id === materialId);
  if (material) {
    // 设置单位
    if (material.unit_id) {
      form.details[index].unit_id = material.unit_id;
    }
    // 保存物料编码、名称和规格型号，方便后续显示
    form.details[index].material_code = material.code;
    form.details[index].material_name = material.name;
    form.details[index].material_specs = material.specs || '';
  }
};

// 过滤物料选项
const filterMaterial = (query) => {
  if (query !== '') {
    return materialOptions.value.filter(item => {
      return item.code.toLowerCase().includes(query.toLowerCase()) || 
             item.name.toLowerCase().includes(query.toLowerCase()) ||
             (item.specs && item.specs.toLowerCase().includes(query.toLowerCase()));
    });
  }
  return materialOptions.value;
};

// 提交表单
const submitForm = () => {
  // 检查是否有BOM明细
  if (form.details.length === 0) {
    ElMessage.warning('请添加至少一条BOM明细');
    return;
  }
  
  // 检查明细数据是否完整
  const invalidDetail = form.details.find(detail => !detail.material_id || !detail.quantity || !detail.unit_id);
  if (invalidDetail) {
    ElMessage.warning('请完善BOM明细信息');
    return;
  }
  
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 检查版本冲突（仅在新增时检查）
        if (!isEdit.value) {
          const existingBom = tableData.value.find(bom =>
            bom.product_id === Number(form.product_id) &&
            bom.version === form.version
          );

          if (existingBom) {
            ElMessage.error(`该产品已存在版本 ${form.version} 的BOM`);
            return;
          }
        }

        // 构造详细完整的请求数据
        const requestData = {
          bomData: {
            product_id: Number(form.product_id),
            version: form.version,
            status: false, // 默认为未审核状态(布尔值false)
            approved: false, // 默认为未审核状态
            remark: form.remark || '',
            attachment: form.attachment, // 确保将attachment传递给后端
            created_by: isEdit.value ? undefined : getUserDisplayName(), // 新增时添加创建人
            updated_by: getUserDisplayName() // 每次更新都添加修改人
          },
          details: form.details.map(detail => ({
            material_id: Number(detail.material_id),
            quantity: Number(detail.quantity),
            unit_id: Number(detail.unit_id),
            remark: detail.remark || ''
          }))
        };

        // 使用baseDataApi直接调用
        if (isEdit.value) {
          await baseDataApi.updateBom(form.id, requestData);
        } else {
          await baseDataApi.createBom(requestData);
        }

        const productName = materialOptions.value.find(m => m.id === Number(form.product_id))?.name || '产品';
        ElMessage.success(isEdit.value ?
          `${productName} BOM版本 ${form.version} 更新成功` :
          `${productName} BOM版本 ${form.version} 创建成功`
        );
        dialogVisible.value = false;
        loadData();
      } catch (error) {
        if (error.response) {
          ElMessage.error(`保存BOM失败: ${error.response.data.message || '未知错误'}`);
        } else {
          ElMessage.error('保存BOM失败: 网络错误');
        }
      }
    }
  });
};

// 导出数据
const handleExport = async () => {
  // 检查是否有数据可导出
  if (selectionMode.value && selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要导出的BOM');
    return;
  }

  if (!selectionMode.value && tableData.value.length === 0) {
    ElMessage.warning('暂无数据可导出');
    return;
  }

  try {
    ElMessage.info('正在导出，请稍候...');

    let exportParams = {};

    if (selectionMode.value && selectedRows.value.length > 0) {
      // 导出选中的BOM
      const selectedIds = selectedRows.value.map(row => row.id);
      exportParams.ids = selectedIds.join(',');
      console.log('导出选中的BOM IDs:', selectedIds);
    } else {
      // 导出所有符合条件的BOM
      exportParams = {
        productId: searchForm.productId,
        version: searchForm.version,
        status: searchForm.status
      };
    }

    const response = await baseDataApi.exportBoms(exportParams);

    // 处理文件下载
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `BOM列表_${new Date().toLocaleDateString().replace(/\//g, '-')}.xlsx`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    const errorMessage = error.response?.data?.message || '导出失败';
    ElMessage.error(errorMessage);
  }
};

// 计算统计数据
const calculateStats = () => {
  stats.total = tableData.value.length;
  stats.active = tableData.value.filter(item => item.approved).length;
  stats.inactive = tableData.value.filter(item => !item.approved).length;

  // 计算所有BOM的明细总数和总成本
  let detailsCount = 0;
  let totalCost = 0;

  tableData.value.forEach(bom => {
    if (bom.details && Array.isArray(bom.details)) {
      detailsCount += bom.details.length;

      // 计算每个BOM的成本
      bom.details.forEach(detail => {
        const material = materialOptions.value.find(m => m.id === detail.material_id);
        if (material && material.price && detail.quantity) {
          totalCost += Number(material.price) * Number(detail.quantity);
        }
      });
    }
  });

  stats.detailsCount = detailsCount;
  stats.totalCost = totalCost;
};

// 格式化货币显示
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2
  }).format(amount || 0);
};



// 产品选择变更
const handleProductChange = async (productId) => {
  if (!productId) {
    return;
  }

  // 查找对应的产品信息
  const product = materialOptions.value.find(item => item.id === productId);

  if (!product && materialOptions.value.length === 0) {
    // 如果未找到且物料选项为空，尝试重新获取物料数据
    fetchMaterials();
  }

  // 如果是新增模式，自动生成该产品的下一个版本号
  if (!isEdit.value) {
    try {
      const nextVersion = await getLatestVersionForProduct(productId);
      form.version = nextVersion;
    } catch (error) {
      console.error('生成版本号失败:', error);
      form.version = generateInitialVersion();
    }
  }
};

// 更多命令处理
const handleMoreCommand = (command) => {
  if (command === 'toggleSelect') {
    // 切换选择模式
    selectionMode.value = !selectionMode.value;
    if (!selectionMode.value) {
      // 退出选择模式时清空选择
      selectedRows.value = [];
      if (tableRef.value) {
        tableRef.value.clearSelection();
      }
    }
    ElMessage.success(selectionMode.value ? '已开启选择模式' : '已关闭选择模式');
  } else if (command === 'copyBom') {
    // 实现复制BOM的逻辑
    fetchBomOptions();
    copyDialogVisible.value = true;
  } else if (command === 'replaceBom') {
    // 实现替换BOM的逻辑
    fetchBomOptions();
    replaceDialogVisible.value = true;
  } else if (command === 'exportBom') {
    // 实现导出BOM的逻辑
    handleExport();
  } else if (command === 'importBom') {
    // 实现导入BOM的逻辑
    importDialogVisible.value = true;
  } else if (command === 'locatePart') {
    // 实现定位功能的逻辑
    locateForm.partCode = ''; // 清空上次查询的编码
    locateResults.value = []; // 清空上次查询的结果
    searchPerformed.value = false; // 重置搜索状态
    locatePartDialogVisible.value = true;
  }
};

// 获取BOM选项列表
const fetchBomOptions = async () => {
  try {
    loading.value = true;
    const response = await baseDataApi.getBoms({ pageSize: 1000 });

    if (response.data && response.data.data) {
      bomOptions.value = response.data.data;
    } else if (Array.isArray(response.data)) {
      bomOptions.value = response.data;
    } else {
      bomOptions.value = [];
    }
  } catch (error) {
    ElMessage.error('获取BOM选项失败');
  } finally {
    loading.value = false;
  }
};

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
  console.log('选中的行数:', selection.length);
  console.log('选中的数据:', selection);
};

// 提交复制
const submitCopy = async () => {
  if (!copyForm.bomId || !copyForm.targetProductId || !copyForm.newVersion) {
    ElMessage.warning('请选择源BOM、目标产品和输入新版本号');
    return;
  }
  
  try {
    loading.value = true;
    
    // 获取源BOM信息
    const sourceBom = bomOptions.value.find(b => b.id === copyForm.bomId);
    if (!sourceBom) {
      throw new Error('未找到源BOM信息');
    }
    
    // 获取目标产品信息
    const targetProduct = materialOptions.value.find(m => m.id === copyForm.targetProductId);
    if (!targetProduct) {
      throw new Error('未找到目标产品信息');
    }
    
    // 检查是否在同一产品内复制BOM（版本更新）
    if (sourceBom.product_id === copyForm.targetProductId) {
      // 检查版本是否已存在
      const existingBom = bomOptions.value.find(b => 
        b.product_id === copyForm.targetProductId && 
        b.version === copyForm.newVersion
      );
      
      if (existingBom) {
        throw new Error(`产品 ${targetProduct.code} 已存在版本 ${copyForm.newVersion} 的BOM`);
      }
    }
    
    // 1. 获取源BOM详情
    const sourceBomResponse = await baseDataApi.getBom(copyForm.bomId);

    if (!sourceBomResponse.data) {
      throw new Error('获取源BOM详情失败');
    }

    // 处理不同的数据结构
    let sourceBomData = sourceBomResponse.data;
    // 检查是否有data包装层
    if (sourceBomData.data && typeof sourceBomData.data === 'object') {
      sourceBomData = sourceBomData.data;
    }
    
    // 确保details是数组且不为空
    let details = [];
    if (Array.isArray(sourceBomData.details)) {
      details = sourceBomData.details;
    } else if (sourceBomData.details_string && typeof sourceBomData.details_string === 'string') {
      try {
        details = JSON.parse(sourceBomData.details_string);
        if (!Array.isArray(details)) {
          details = [];
        }
      } catch (e) {
        details = [];
      }
    }
    
    if (details.length === 0) {
      throw new Error('源BOM无物料明细，无法复制');
    }
    
    // 验证明细数据的完整性
    details = details.filter(detail => {
      return detail.material_id && detail.quantity && detail.unit_id;
    });
    
    if (details.length === 0) {
      throw new Error('源BOM明细数据无效，无法复制');
    }
    
    // 格式化明细数据，确保字段类型正确
    details = details.map(detail => ({
      material_id: Number(detail.material_id),
      quantity: Number(detail.quantity || 1),
      unit_id: Number(detail.unit_id),
      remark: detail.remark || ''
    }));
    
    // 2. 创建新BOM数据
    const submitData = {
      bomData: {
        product_id: Number(copyForm.targetProductId),
        version: copyForm.newVersion,
        status: Number(sourceBomData.status || 1),
        remark: sourceBomData.remark || `从产品 ${sourceBom.product_code || sourceBom.product?.code || '未知'} 的BOM复制而来`,
        created_by: getUserDisplayName(),
        updated_by: getUserDisplayName()
      },
      details: details
    };

    // 3. 创建新BOM
    await baseDataApi.createBom(submitData);

    ElMessage.success(`成功将BOM从 ${sourceBom.product_code || '未知产品'} 复制到 ${targetProduct.code}`);
    copyDialogVisible.value = false;

    // 清空表单
    copyForm.bomId = null;
    copyForm.targetProductId = null;
    copyForm.newVersion = '';

    // 刷新BOM列表
    loadData();
  } catch (error) {
    ElMessage.error('复制BOM失败: ' + (error.response?.data?.message || error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 定位零部件相关方法
const searchPart = async () => {
  if (!locateForm.partCode) {
    ElMessage.warning('请输入零部件编码');
    return;
  }
  
  try {
    loading.value = true;
    searchPerformed.value = true;
    
    // 获取原始搜索编码
    const originalPartCode = locateForm.partCode.trim();

    try {
      // 尝试使用后端API查询
      const response = await baseDataApi.locatePart(originalPartCode);

      if (response.data && Array.isArray(response.data)) {
        locateResults.value = response.data;
      } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
        locateResults.value = response.data.data;
      } else {
        throw new Error('API返回数据格式不正确');
      }
    } catch (apiError) {
      // 前端搜索实现
      const simplifiedPartCode = originalPartCode.replace(/^0+/, '');
      
      // 获取所有BOM数据
      const response = await baseDataApi.getBoms({ pageSize: 1000 });
      let bomsList = [];
      if (response.data && response.data.data) {
        bomsList = response.data.data;
      } else if (Array.isArray(response.data)) {
        bomsList = response.data;
      }
      
      // 在前端筛选包含指定零部件的BOM
      const filteredBoms = [];
      
      // 获取每个BOM的详细信息并搜索
      for (const bom of bomsList) {
        try {
          const bomDetailResponse = await baseDataApi.getBom(bom.id);
          if (!bomDetailResponse.data) continue;
          
          const bomDetail = bomDetailResponse.data;
          const bomDetails = Array.isArray(bomDetail.details) ? bomDetail.details : [];
          
          for (const detail of bomDetails) {
            const materialCode = detail.material_code || '';
            if (!materialCode) continue;
            
            // 尝试不同的匹配方式
            const directMatch = materialCode.trim() === originalPartCode;
            const simplifiedMatch = materialCode.trim().replace(/^0+/, '') === simplifiedPartCode;
            const containsMatch = materialCode.includes(originalPartCode) || 
                                originalPartCode.includes(materialCode);
            
            if (directMatch || simplifiedMatch || containsMatch) {
              filteredBoms.push({
                ...bom,
                ...bomDetail,
                quantity: detail.quantity || 1,
                unit_name: detail.unit_name || '个',
                material_code: materialCode,
                material_name: detail.material_name || '未知',
                material_id: detail.material_id
              });
              break;
            }
          }
        } catch (error) {
          // 忽略处理错误，继续处理下一个BOM
        }
      }

      locateResults.value = filteredBoms;
    }

    // 显示结果
    if (locateResults.value.length === 0) {
      ElMessage.info('未找到包含该零部件的BOM');
    } else {
      ElMessage.success(`找到${locateResults.value.length}个包含该零部件的BOM`);
    }
  } catch (error) {
    ElMessage.error('定位零部件失败');
  } finally {
    loading.value = false;
  }
};

const viewBomDetail = (row) => {
  // 实际BOM数据，调用已有的handleView函数查看BOM详情
  handleView(row);
  locatePartDialogVisible.value = false; // 关闭定位对话框
};

// 监听全选/取消全选
const handleSelectAllChange = (val) => {
  selectAll.value = val;
  if (val) {
    // 全选
    locateResults.value.forEach(row => {
      tableRef.value?.toggleRowSelection(row, true);
    });
  } else {
    // 取消全选
    tableRef.value?.clearSelection();
  }
  updateSelectedLocateResults();
};

// 处理定位结果选择变更
const handleLocateSelectionChange = (selection) => {
  selectedLocateResults.value = selection;
  // 更新全选状态
  selectAll.value = selection.length === locateResults.value.length && selection.length > 0;
};

// 处理单个零部件替换
const handleReplaceSinglePart = (row) => {
  replacePartForm.currentPartCode = row.material_code;
  replacePartForm.currentBomIds = [row.id];
  replacePartForm.newPartId = null;
  replacePartForm.keepQuantity = true;
  replacePartForm.newQuantity = Number(row.quantity) || 1;
  replacePartDialogVisible.value = true;
};

// 处理批量替换零部件
const handleReplaceParts = () => {
  if (selectedLocateResults.value.length === 0) {
    ElMessage.warning('请先选择需要替换的BOM');
    return;
  }

  // 检查所选BOM是否包含相同的零部件
  const firstCode = selectedLocateResults.value[0].material_code;
  const allSamePart = selectedLocateResults.value.every(item => item.material_code === firstCode);

  if (!allSamePart) {
    ElMessage.warning('批量替换仅支持替换相同的零部件');
    return;
  }
  replacePartForm.currentPartCode = firstCode;
  replacePartForm.currentBomIds = selectedLocateResults.value.map(item => item.id);
  replacePartForm.newPartId = null;
  replacePartForm.keepQuantity = true;
  replacePartForm.newQuantity = 1;
  replacePartDialogVisible.value = true;
};

// 提交零部件替换
const submitReplacePart = async () => {
  if (!replacePartForm.newPartId) {
    ElMessage.warning('请选择替换的零部件');
    return;
  }
  
  try {
    loading.value = true;
    
    // 获取新物料信息
    const newMaterial = materialOptions.value.find(m => m.id === replacePartForm.newPartId);
    if (!newMaterial) {
      throw new Error('无法找到选择的替换物料');
    }

    // 对每个选中的BOM进行替换
    for (const bomId of replacePartForm.currentBomIds) {
      // 获取BOM详情
      const bomResponse = await baseDataApi.getBom(bomId);

      if (!bomResponse.data) {
        continue;
      }

      // 解析BOM数据
      let bomData = bomResponse.data;
      if (bomData.data && typeof bomData.data === 'object') {
        bomData = bomData.data;
      }

      // 确保details是数组
      if (!Array.isArray(bomData.details) || bomData.details.length === 0) {
        continue;
      }

      // 查找要替换的零部件明细
      const oldPartIndex = bomData.details.findIndex(detail => {
        const materialCode = detail.material_code || '';
        return materialCode === replacePartForm.currentPartCode;
      });

      if (oldPartIndex === -1) {
        continue;
      }

      // 获取原零部件数据
      const oldPart = bomData.details[oldPartIndex];
      
      // 替换零部件
      const newPartDetail = {
        ...oldPart,
        material_id: replacePartForm.newPartId,
        material_code: newMaterial.code,
        material_name: newMaterial.name,
        material_specs: newMaterial.specs || '',
        quantity: replacePartForm.keepQuantity ? Number(oldPart.quantity) : Number(replacePartForm.newQuantity)
      };
      
      // 更新明细
      bomData.details[oldPartIndex] = newPartDetail;
      
      // 构造更新请求数据
      const updateData = {
        bomData: {
          product_id: bomData.product_id,
          version: bomData.version,
          status: Number(bomData.status || 1),
          remark: bomData.remark || '',
          updated_by: getUserDisplayName() // 添加修改人信息
        },
        details: bomData.details.map(detail => ({
          ...detail,
          quantity: Number(detail.quantity || 1),
          unit_id: Number(detail.unit_id)
        }))
      };

      // 更新BOM
      await baseDataApi.updateBom(bomId, updateData);
    }

    ElMessage.success('零部件替换成功');
    replacePartDialogVisible.value = false;

    // 刷新查询结果
    searchPart();
  } catch (error) {
    ElMessage.error('替换零部件失败: ' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 处理源BOM变更
const handleSourceBomChange = (bomId) => {
  // 防止源和目标选择同一个BOM
  if (replaceForm.targetBomId === bomId) {
    replaceForm.targetBomId = null;
  }
};

// 提交替换
const submitReplace = async () => {
  if (!replaceForm.sourceBomId || !replaceForm.targetBomId) {
    ElMessage.warning('请选择源BOM和目标BOM');
    return;
  }
  
  if (replaceForm.sourceBomId === replaceForm.targetBomId) {
    ElMessage.warning('源BOM和目标BOM不能相同');
    return;
  }
  
  try {
    loading.value = true;
    await baseDataApi.replaceBom({
      sourceBomId: replaceForm.sourceBomId,
      targetBomId: replaceForm.targetBomId,
      mode: replaceForm.replaceMode
    });

    ElMessage.success('替换成功');
    replaceDialogVisible.value = false;

    // 清空表单
    replaceForm.sourceBomId = null;
    replaceForm.targetBomId = null;

    // 刷新BOM列表
    loadData();
  } catch (error) {
    ElMessage.error('替换BOM失败');
  } finally {
    loading.value = false;
  }
};

// 更新选择结果
const updateSelectedLocateResults = () => {
  selectedLocateResults.value = locateResults.value.filter(item => {
    return selectedLocateResults.value.some(selected => selected.id === item.id);
  });
};

// 处理附件选择
const handleAttachmentChange = async (file) => {
  // 将文件添加到fileList中显示
  if (!fileList.value.some(f => f.name === file.name)) {
    fileList.value.push(file);
  }

  try {
    // 创建FormData
    const formData = new FormData();
    formData.append('file', file.raw);

    // 获取token
    const token = localStorage.getItem('token');
    
    // 上传文件
    const response = await baseDataApi.uploadFile(formData);

    // 保存返回的文件URL
    if (response.data && response.data.success && response.data.fileUrl) {
      form.attachment = response.data.fileUrl;
      ElMessage.success('附件上传成功');
    } else if (response.data && response.data.fileUrl) {
      // 兼容旧格式的返回
      form.attachment = response.data.fileUrl;
      ElMessage.success('附件上传成功');
    } else {
      throw new Error(response.data?.message || '上传失败，未返回有效的文件URL');
    }
  } catch (error) {
    ElMessage.error('附件上传失败: ' + (error.response?.data?.message || error.message || '未知错误'));
  }
};

// 处理附件移除
const handleAttachmentRemove = () => {
  form.attachment = null;
  ElMessage.info('附件已移除');
};

// 处理文件选择
const handleFileChange = (file) => {
  selectedFile.value = file.raw;
};



// 提交导入
const submitImport = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择文件');
    return;
  }
  
  try {
    loading.value = true;
    const formData = new FormData();
    formData.append('file', selectedFile.value);
    
    // 调用后端API导入BOM数据
    await baseDataApi.importBoms(formData);

    ElMessage.success('导入成功');
    importDialogVisible.value = false;

    // 刷新BOM列表
    loadData();
  } catch (error) {
    ElMessage.error('导入BOM失败: ' + (error.response?.data?.message || error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 辅助函数
const isImageFile = (filePath) => {
  if (!filePath) return false;
  const lower = filePath.toLowerCase();
  return lower.endsWith('.jpg') || lower.endsWith('.jpeg') || lower.endsWith('.png');
};

const isPdfFile = (filePath) => {
  if (!filePath) return false;
  const lower = filePath.toLowerCase();
  return lower.endsWith('.pdf');
};

const getAttachmentUrl = (filePath) => {
  // 将相对路径转换为完整URL
  if (filePath.startsWith('/')) {
    // 使用当前域名 + 文件路径，通过Vite代理访问
    return window.location.origin + filePath;
  }
  return filePath;
};

const openAttachment = (filePath) => {
  const url = getAttachmentUrl(filePath);
  window.open(url, '_blank');
};

const downloadAttachment = (filePath) => {
  const url = getAttachmentUrl(filePath);
  
  // 创建一个隐藏的a标签用于下载
  const a = document.createElement('a');
  a.style.display = 'none';
  a.href = url;
  
  // 从URL中提取文件名
  const fileName = filePath.split('/').pop();
  a.download = fileName;
  
  document.body.appendChild(a);
  a.click();
  
  // 清理DOM
  setTimeout(() => {
    document.body.removeChild(a);
  }, 100);
};

// 获取用户显示名称
const getUserDisplayName = () => {
  try {
    const userStr = localStorage.getItem('user');

    if (!userStr) return '系统用户';

    const user = JSON.parse(userStr);

    // 检查所有可能的用户名字段
    const possibleFields = ['real_name', 'realName', 'name', 'username', 'displayName'];
    for (const field of possibleFields) {
      if (user[field]) {
        return user[field];
      }
    }

    return '系统用户';
  } catch (error) {
    return '系统用户';
  }
};

// 审核BOM
const handleApprove = async (row) => {
  try {
    loading.value = true;
    await baseDataApi.approveBom(row.id);

    // 更新表格中的数据
    const index = tableData.value.findIndex(item => item.id === row.id);
    if (index !== -1) {
      tableData.value[index].status = true;
      tableData.value[index].approved = true;
    }

    ElMessage.success('BOM审核成功');
    calculateStats(); // 重新计算统计数据
  } catch (error) {
    console.error('审核BOM失败:', error);
    const errorMessage = error.response?.data?.message || '审核BOM失败';
    ElMessage.error(errorMessage);
  } finally {
    loading.value = false;
  }
};

// 反审核BOM
const handleUnapprove = async (row) => {
  try {
    loading.value = true;
    await baseDataApi.unapproveBom(row.id);

    // 更新表格中的数据
    const index = tableData.value.findIndex(item => item.id === row.id);
    if (index !== -1) {
      tableData.value[index].status = false;
      tableData.value[index].approved = false;
    }

    ElMessage.success('BOM反审核成功');
    calculateStats(); // 重新计算统计数据
  } catch (error) {
    console.error('反审核BOM失败:', error);
    const errorMessage = error.response?.data?.message || '反审核BOM失败';
    ElMessage.error(errorMessage);
  } finally {
    loading.value = false;
  }
};

// 辅助函数
const formatDate = (dateString) => {
  if (!dateString) return '';
  try {
    return new Date(dateString).toLocaleDateString();
  } catch {
    return dateString;
  }
};

const formatStatus = (status) => {
  return status ? '已审核' : '未审核';
};

const getStatusType = (status) => {
  return status ? 'success' : 'warning';
};

// 版本管理辅助函数
const generateInitialVersion = () => {
  return 'V1.0';
};

const incrementVersion = (currentVersion) => {
  if (!currentVersion) return 'V1.0';

  // 匹配版本格式 V1.0, V1.1, V2.0 等
  const versionMatch = currentVersion.match(/^V(\d+)\.(\d+)$/);
  if (versionMatch) {
    const major = parseInt(versionMatch[1]);
    const minor = parseInt(versionMatch[2]);
    return `V${major}.${minor + 1}`;
  }

  // 如果格式不匹配，尝试其他常见格式
  const simpleMatch = currentVersion.match(/^(\d+)\.(\d+)$/);
  if (simpleMatch) {
    const major = parseInt(simpleMatch[1]);
    const minor = parseInt(simpleMatch[2]);
    return `V${major}.${minor + 1}`;
  }

  // 如果都不匹配，返回默认版本
  return 'V1.0';
};

// 获取产品的最新版本号
const getLatestVersionForProduct = async (productId) => {
  try {
    // 获取该产品的所有BOM
    const response = await baseDataApi.getBoms({
      productId: productId,
      pageSize: 1000 // 获取所有版本
    });

    let bomList = [];
    if (response.data && response.data.data) {
      bomList = response.data.data;
    } else if (Array.isArray(response.data)) {
      bomList = response.data;
    }

    // 过滤出该产品的BOM并按版本排序
    const productBoms = bomList
      .filter(bom => bom.product_id === productId)
      .map(bom => bom.version)
      .filter(version => version);

    if (productBoms.length === 0) {
      return 'V1.0';
    }

    // 找到最高版本号
    let maxVersion = 'V1.0';
    productBoms.forEach(version => {
      if (compareVersions(version, maxVersion) > 0) {
        maxVersion = version;
      }
    });

    return incrementVersion(maxVersion);
  } catch (error) {
    console.error('获取最新版本失败:', error);
    return 'V1.0';
  }
};

// 版本比较函数
const compareVersions = (version1, version2) => {
  const v1Match = version1.match(/^V?(\d+)\.(\d+)$/);
  const v2Match = version2.match(/^V?(\d+)\.(\d+)$/);

  if (!v1Match || !v2Match) return 0;

  const v1Major = parseInt(v1Match[1]);
  const v1Minor = parseInt(v1Match[2]);
  const v2Major = parseInt(v2Match[1]);
  const v2Minor = parseInt(v2Match[2]);

  if (v1Major !== v2Major) {
    return v1Major - v2Major;
  }
  return v1Minor - v2Minor;
};
</script>

<style scoped>
.boms-container {
  padding: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.search-card {
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}

.statistics-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 15px;
}

.stat-card {
  flex: 1;
  min-width: 150px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.data-card {
  margin-bottom: 20px;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.bom-details {
  margin-top: 15px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.delete-text-btn {
  color: #F56C6C;
  padding: 0 4px;
}

.delete-text-btn:hover {
  color: #f78989;
}

.attachment-section {
  margin-top: 20px;
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.attachment-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.attachment-preview {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.pdf-preview {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pdf-actions, .other-file-preview {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.other-file-preview {
  display: flex;
  justify-content: center;
  padding: 30px;
  background-color: #f5f7fa;
  border-radius: 4px;
  width: 100%;
}

/* 表格优化 */
.el-table .cell {
  padding: 0 8px;
}

/* 状态标签优化 */
.el-tag {
  font-weight: 500;
}

/* 搜索表单优化 */
.search-form .el-form-item {
  margin-bottom: 10px;
}

/* 按钮组优化 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 版本输入框优化 */
.el-input[readonly] .el-input__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
  cursor: not-allowed;
}

/* 版本提示文字 */
.version-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}
</style>