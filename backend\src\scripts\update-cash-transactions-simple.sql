ALTER TABLE cash_transactions 
ADD COLUMN created_by INT NULL AFTER transaction_number,
ADD INDEX idx_created_by (created_by);

UPDATE cash_transactions SET created_by = 1 WHERE created_by IS NULL;

ALTER TABLE cash_transactions 
ADD INDEX idx_amount (amount),
ADD INDEX idx_created_at (created_at),
ADD INDEX idx_updated_at (updated_at);

CREATE TABLE IF NOT EXISTS cash_transaction_categories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  type ENUM('income', 'expense') NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  sort_order INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_type (type),
  INDEX idx_is_active (is_active),
  INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO cash_transaction_categories (code, name, type, description, sort_order) VALUES
('sales', 'Sales Income', 'income', 'Product or service sales income', 1),
('other_income', 'Other Income', 'income', 'Other income except sales', 2),
('office', 'Office Expenses', 'expense', 'Office supplies and stationery', 1),
('travel', 'Travel Expenses', 'expense', 'Business trip and accommodation', 2),
('meal', 'Meal Expenses', 'expense', 'Employee meals and entertainment', 3),
('other_expense', 'Other Expenses', 'expense', 'Other miscellaneous expenses', 4)
ON DUPLICATE KEY UPDATE 
name = VALUES(name),
description = VALUES(description),
sort_order = VALUES(sort_order);
