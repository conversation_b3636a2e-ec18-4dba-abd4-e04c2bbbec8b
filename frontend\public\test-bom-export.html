<!DOCTYPE html>
<html>
<head>
    <title>BOM导出测试</title>
</head>
<body>
    <h1>BOM导出API测试</h1>
    <button onclick="testBomExport()">测试BOM导出</button>
    <div id="result"></div>

    <script>
        async function testBomExport() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在测试...';
            
            try {
                // 获取token
                const token = localStorage.getItem('token');
                console.log('Token:', token);
                
                const response = await fetch('/api/baseData/boms/export?productId=&version=', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (response.ok) {
                    const blob = await response.blob();
                    console.log('Blob size:', blob.size);
                    
                    // 创建下载链接
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'bom_export_test.xlsx';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                    
                    resultDiv.innerHTML = `
                        <h3>导出成功!</h3>
                        <p>状态码: ${response.status}</p>
                        <p>文件大小: ${blob.size} bytes</p>
                    `;
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `
                        <h3>导出失败:</h3>
                        <p style="color: red;">状态码: ${response.status}</p>
                        <p style="color: red;">错误信息: ${errorText}</p>
                    `;
                }
                
            } catch (error) {
                console.error('API测试失败:', error);
                resultDiv.innerHTML = `
                    <h3>API测试失败:</h3>
                    <p style="color: red;">${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
