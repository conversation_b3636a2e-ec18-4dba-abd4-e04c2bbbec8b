CREATE TABLE IF NOT EXISTS cash_transactions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  transaction_type ENUM('income', 'expense') NOT NULL,
  transaction_date DATE NOT NULL,
  amount DECIMAL(15,2) NOT NULL,
  category VARCHAR(50) NOT NULL,
  counterparty VARCHAR(200),
  description TEXT NOT NULL,
  reference_number VARCHAR(100),
  transaction_number VARCHAR(100) UNIQUE NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_transaction_date (transaction_date),
  INDEX idx_transaction_type (transaction_type),
  INDEX idx_category (category),
  INDEX idx_transaction_number (transaction_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


INSERT INTO cash_transactions (
  transaction_type, transaction_date, amount, category, counterparty, 
  description, reference_number, transaction_number
) VALUES 
('income', '2024-01-15', 5000.00, 'sales', '客户A', '销售收入', 'SH001', 'CASH1705123456001'),
('expense', '2024-01-16', 300.00, 'office', '办公用品店', '购买办公用品', 'BG001', 'CASH1705123456002'),
('income', '2024-01-17', 2000.00, 'other_income', '退款', '供应商退款', 'TK001', 'CASH1705123456003'),
('expense', '2024-01-18', 150.00, 'meal', '餐厅', '员工聚餐', 'CY001', 'CASH1705123456004'),
('expense', '2024-01-19', 800.00, 'travel', '差旅公司', '出差费用', 'CC001', 'CASH1705123456005'),
('income', '2024-01-20', 3500.00, 'sales', '客户B', '现金销售', 'SH002', 'CASH1705123456006'),
('expense', '2024-01-21', 200.00, 'office', '文具店', '购买文具', 'BG002', 'CASH1705123456007'),
('expense', '2024-01-22', 450.00, 'other_expense', '维修公司', '设备维修', 'WX001', 'CASH1705123456008'),
('income', '2024-01-23', 1200.00, 'other_income', '利息收入', '银行利息', 'LX001', 'CASH1705123456009'),
('expense', '2024-01-24', 600.00, 'travel', '酒店', '住宿费用', 'ZS001', 'CASH1705123456010');
