﻿﻿<template>
  <div class="transactions-container">
    <div class="page-header">
      <h2>现金交易管理</h2>
      <div class="action-buttons">
        <el-button type="primary" @click="showAddDialog">新增交易</el-button>
        <el-button type="success" @click="exportTransactions">导出数据</el-button>
        <el-button type="warning" @click="showImportDialog">导入数据</el-button>
        <el-button type="primary" plain @click="printCashStatement">打印</el-button>
      </div>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="交易日期">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="交易类型">
          <el-select v-model="searchForm.type" placeholder="选择类型" clearable style="width: 160px;">
            <el-option label="收入" value="income"></el-option>
            <el-option label="支出" value="expense"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交易分类">
          <el-select v-model="searchForm.category" placeholder="选择分类" clearable style="width: 160px;">
            <el-option label="销售收入" value="sales"></el-option>
            <el-option label="其他收入" value="other_income"></el-option>
            <el-option label="办公费用" value="office"></el-option>
            <el-option label="差旅费" value="travel"></el-option>
            <el-option label="餐饮费" value="meal"></el-option>
            <el-option label="其他支出" value="other_expense"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="loadTransactions">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card income">
            <div class="stat-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatCurrency(transactionStats.totalIncome) }}</div>
              <div class="stat-label">总收入</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card expense">
            <div class="stat-icon">
              <el-icon><Minus /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatCurrency(transactionStats.totalExpense) }}</div>
              <div class="stat-label">总支出</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card balance">
            <div class="stat-icon">
              <el-icon><Wallet /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatCurrency(transactionStats.netAmount) }}</div>
              <div class="stat-label">净额</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card count">
            <div class="stat-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ transactionStats.totalCount }}</div>
              <div class="stat-label">交易笔数</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <el-card class="data-card">
      <el-table
        :data="transactionList" 
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :default-sort="{ prop: 'transactionDate', order: 'descending' }"
      >
        <el-table-column prop="transactionDate" label="交易日期" width="120" sortable>
          <template #default="scope">
            {{ formatDate(scope.row.transactionDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="transactionNumber" label="交易号" width="200" />
        <el-table-column prop="type" label="类型" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.type === 'income' ? 'success' : 'danger'">
              {{ getTransactionTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="分类" width="120">
          <template #default="scope">
            {{ getCategoryText(scope.row.category) }}
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="金额" width="120" sortable>
          <template #default="scope">
            <span :class="scope.row.type === 'income' ? 'amount-income' : 'amount-expense'">
              {{ formatCurrency(scope.row.amount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="counterparty" label="交易对方" width="150" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="referenceNumber" label="凭证号" width="120" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="editTransaction(scope.row)">编辑</el-button>
            <el-button type="danger" size="small" @click="deleteTransaction(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :small="false"
          :disabled="false"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="Math.max(total, 1)"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="transactionFormRef"
        :model="transactionForm"
        :rules="transactionRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="交易类型" prop="type">
              <el-select v-model="transactionForm.type" placeholder="请选择">
                <el-option label="收入" value="income" />
                <el-option label="支出" value="expense" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="交易日期" prop="transactionDate">
              <el-date-picker
                v-model="transactionForm.transactionDate"
                type="date"
                placeholder="选择日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="金额" prop="amount">
              <el-input-number
                v-model="transactionForm.amount"
                :min="0"
                :precision="2"
                placeholder="请输入金额"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分类" prop="category">
              <el-select v-model="transactionForm.category" placeholder="请选择">
                <template v-if="transactionForm.type === 'income'">
                  <el-option label="销售收入" value="sales" />
                  <el-option label="其他收入" value="other_income" />
                </template>
                <template v-else>
                  <el-option label="办公费用" value="office" />
                  <el-option label="差旅费" value="travel" />
                  <el-option label="餐饮费" value="meal" />
                  <el-option label="其他支出" value="other_expense" />
                </template>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="交易对方" prop="counterparty">
          <el-input v-model="transactionForm.counterparty" placeholder="请输入交易对方" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="transactionForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入交易描述"
          />
        </el-form-item>

        <el-form-item label="凭证号" prop="referenceNumber">
          <el-input v-model="transactionForm.referenceNumber" placeholder="请输入凭证号" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveTransaction" :loading="saveLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入现金交易数据"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        action="#"
        :auto-upload="false"
        :file-list="importFileList"
        :on-change="handleFileChange"
        :before-remove="handleFileRemove"
        accept=".xlsx,.xls,.csv"
      >
        <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只能上传 xlsx/xls/csv 文件，且不超过 10MB
          </div>
        </template>
      </el-upload>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="importTransactions" :loading="importLoading">
            导入
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { UploadFilled, TrendCharts, Minus, Wallet, Document } from '@element-plus/icons-vue';
import { api } from '@/services/api';

// 数据加载状态
const loading = ref(false);
const saveLoading = ref(false);

// 分页相关
const total = ref(0);
const pageSize = ref(10);
const currentPage = ref(1);

// 对话框状态
const dialogVisible = ref(false);
const dialogTitle = ref('新增交易');
const importDialogVisible = ref(false);

// 表单相关
const transactionFormRef = ref(null);
const uploadRef = ref(null);

// 导入相关
const importLoading = ref(false);
const importFileList = ref([]);
const importResult = ref(null);

// 数据列表
const transactionList = ref([]);

// 交易统计
const transactionStats = reactive({
  totalCount: 0,
  totalIncome: 0,
  totalExpense: 0,
  netAmount: 0
});

// 搜索表单
const searchForm = reactive({
  dateRange: null,
  type: '',
  category: ''
});

// 交易表单
const transactionForm = reactive({
  id: null,
  type: 'income',
  transactionDate: new Date().toISOString().slice(0, 10),
  amount: 0,
  category: '',
  counterparty: '',
  description: '',
  referenceNumber: '',
  transactionNumber: ''
});

// 表单验证规则
const transactionRules = {
  type: [
    { required: true, message: '请选择交易类型', trigger: 'change' }
  ],
  transactionDate: [
    { required: true, message: '请选择交易日期', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '金额必须大于0', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入交易描述', trigger: 'blur' }
  ]
};

// 监听交易类型变化，重置分类
watch(() => transactionForm.type, (newType) => {
  transactionForm.category = '';
});

// 格式化货币
const formatCurrency = (amount) => {
  if (amount === undefined || amount === null) return '¥0.00';
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString('zh-CN');
};

// 获取交易类型文本
const getTransactionTypeText = (type) => {
  const typeMap = {
    income: '收入',
    expense: '支出'
  };
  return typeMap[type] || type;
};

// 获取分类文本
const getCategoryText = (category) => {
  const categoryMap = {
    sales: '销售收入',
    other_income: '其他收入',
    office: '办公费用',
    travel: '差旅费',
    meal: '餐饮费',
    other_expense: '其他支出'
  };
  return categoryMap[category] || category;
};

// 重置搜索
const resetSearch = () => {
  searchForm.dateRange = null;
  searchForm.type = '';
  searchForm.category = '';
  loadTransactions();
};

// 显示新增对话框
const showAddDialog = () => {
  dialogTitle.value = '新增现金交易';
  resetTransactionForm();
  dialogVisible.value = true;
};

// 重置表单
const resetTransactionForm = () => {
  transactionForm.id = null;
  transactionForm.type = 'income';
  transactionForm.transactionDate = new Date().toISOString().slice(0, 10);
  transactionForm.amount = 0;
  transactionForm.category = '';
  transactionForm.counterparty = '';
  transactionForm.description = '';
  transactionForm.referenceNumber = '';
  transactionForm.transactionNumber = '';
};

// 编辑交易
const editTransaction = (row) => {
  dialogTitle.value = '编辑现金交易';
  Object.assign(transactionForm, row);
  dialogVisible.value = true;
};

// 保存交易
const saveTransaction = async () => {
  if (!transactionFormRef.value) return;
  
  try {
    await transactionFormRef.value.validate();
    saveLoading.value = true;
    
    const data = { ...transactionForm };
    
    if (data.id) {
      await api.put(`/finance/cash-transactions/${data.id}`, data);
      ElMessage.success('交易更新成功');
    } else {
      await api.post('/finance/cash-transactions', data);
      ElMessage.success('交易创建成功');
    }
    
    dialogVisible.value = false;
    loadTransactions();
  } catch (error) {
    console.error('保存交易失败:', error);
    ElMessage.error(`保存失败: ${error.response?.data?.message || error.message}`);
  } finally {
    saveLoading.value = false;
  }
};

// 删除交易
const deleteTransaction = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除交易 "${row.description}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    await api.delete(`/finance/cash-transactions/${row.id}`);
    ElMessage.success('交易删除成功');
    loadTransactions();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除交易失败:', error);
      ElMessage.error(`删除失败: ${error.response?.data?.message || error.message}`);
    }
  }
};

// 加载交易列表
const loadTransactions = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      ...searchForm
    };
    
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0];
      params.endDate = searchForm.dateRange[1];
    }
    
    const response = await api.get('/finance/cash-transactions', { params });
    // API返回格式: {success: true, message: "...", data: {transactions: [...], total: 10, page: 1, pageSize: 10}}
    const responseData = response.data.data || response.data;
    transactionList.value = responseData.transactions || [];
    total.value = parseInt(responseData.total) || 0;
    
    // 加载统计数据
    await loadTransactionsStats();
  } catch (error) {
    console.error('加载交易列表失败:', error);
    ElMessage.error('加载交易列表失败');
  } finally {
    loading.value = false;
  }
};

// 加载统计数据
const loadTransactionsStats = async () => {
  try {
    const params = { ...searchForm };
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0];
      params.endDate = searchForm.dateRange[1];
    }
    
    const response = await api.get('/finance/cash-transactions/stats', { params });
    // API返回格式: {success: true, message: "...", data: {totalCount: 10, totalIncome: 1200, ...}}
    const statsData = response.data.data || response.data;
    Object.assign(transactionStats, statsData);
  } catch (error) {
    console.error('加载统计数据失败:', error);
  }
};

// 导出交易
const exportTransactions = async () => {
  try {
    const params = { ...searchForm };
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0];
      params.endDate = searchForm.dateRange[1];
    }
    
    const response = await api.get('/finance/cash-transactions/export', { 
      params,
      responseType: 'blob'
    });
    
    const blob = new Blob([response.data], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `现金交易记录_${new Date().toISOString().slice(0, 10)}.xlsx`;
    link.click();
    window.URL.revokeObjectURL(url);
    
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败');
  }
};

// 显示导入对话框
const showImportDialog = () => {
  importFileList.value = [];
  importDialogVisible.value = true;
};

// 处理文件变化
const handleFileChange = (file, fileList) => {
  importFileList.value = fileList;
};

// 处理文件移除
const handleFileRemove = (file, fileList) => {
  importFileList.value = fileList;
  return true;
};

// 导入交易
const importTransactions = async () => {
  if (importFileList.value.length === 0) {
    ElMessage.warning('请选择要导入的文件');
    return;
  }
  
  try {
    importLoading.value = true;
    const formData = new FormData();
    formData.append('file', importFileList.value[0].raw);
    
    const response = await api.post('/finance/cash-transactions/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    const importData = response.data.data || response.data;
    ElMessage.success(`导入成功，共导入 ${importData.count || 0} 条记录`);
    importDialogVisible.value = false;
    loadTransactions();
  } catch (error) {
    console.error('导入失败:', error);
    ElMessage.error(`导入失败: ${error.response?.data?.message || error.message}`);
  } finally {
    importLoading.value = false;
  }
};

// 打印现金日记账
const printCashStatement = async () => {
  try {
    loading.value = true;

    // 获取当前筛选条件下的所有交易数据
    const params = {
      page: 1,
      pageSize: 1000 // 获取大量数据用于打印
    };

    // 只有当筛选条件不为空时才添加到参数中
    if (searchForm.type) {
      params.type = searchForm.type;
    }
    if (searchForm.category) {
      params.category = searchForm.category;
    }
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0];
      params.endDate = searchForm.dateRange[1];
    }

    const response = await api.get('/finance/cash-transactions', { params });
    const responseData = response.data.data || response.data;
    const printData = responseData.transactions || [];



    if (printData.length === 0) {
      ElMessage.warning('没有找到符合条件的交易数据，请检查筛选条件');
      return;
    }

    ElMessage.success(`准备打印 ${printData.length} 条交易记录`);

    // 尝试获取打印模板
    try {
      const templateResponse = await api.get('/print/templates/public/default', {
        params: {
          module: 'finance',
          template_type: 'cash_statement'
        }
      });

      if (templateResponse.data && templateResponse.data.data) {
        const template = templateResponse.data.data;

        // 使用模板进行打印
        const printWindow = window.open('', '_blank');
        if (!printWindow) {
          ElMessage.error('无法打开打印窗口，请检查浏览器是否阻止弹出窗口');
          return;
        }

        // 替换模板中的变量
        let printContent = template.content;

        // 替换基本信息
        printContent = printContent.replace(/{{companyName}}/g, '浙江开控电气有限公司');
        printContent = printContent.replace(/{{printDate}}/g, new Date().toLocaleDateString('zh-CN'));

        // 生成交易记录表格
        let tableRows = '';
        let runningBalance = 0;

        // 按日期排序
        const sortedData = printData.sort((a, b) => new Date(a.transactionDate) - new Date(b.transactionDate));

        sortedData.forEach((item) => {
          const amount = parseFloat(item.amount);
          const isIncome = item.type === 'income';

          if (isIncome) {
            runningBalance += amount;
          } else {
            runningBalance -= amount;
          }

          tableRows += `
            <tr>
              <td style="border: 1px solid #000; padding: 4px; text-align: center;">${item.transactionDate}</td>
              <td style="border: 1px solid #000; padding: 4px; text-align: center;">${item.referenceNumber || ''}</td>
              <td style="border: 1px solid #000; padding: 4px; text-align: center;">${item.counterparty || ''}</td>
              <td style="border: 1px solid #000; padding: 4px;">${item.description || ''}</td>
              <td style="border: 1px solid #000; padding: 4px; text-align: right;">${isIncome ? amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) : '-'}</td>
              <td style="border: 1px solid #000; padding: 4px; text-align: right;">${!isIncome ? amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) : '-'}</td>
              <td style="border: 1px solid #000; padding: 4px; text-align: right;">${runningBalance.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}</td>
            </tr>
          `;
        });

        printContent = printContent.replace(/{{transactionRows}}/g, tableRows);

        // 计算合计
        const totalIncome = sortedData.filter(item => item.type === 'income').reduce((sum, item) => sum + parseFloat(item.amount), 0);
        const totalExpense = sortedData.filter(item => item.type === 'expense').reduce((sum, item) => sum + parseFloat(item.amount), 0);

        printContent = printContent.replace(/{{totalIncome}}/g, totalIncome.toLocaleString('zh-CN', { minimumFractionDigits: 2 }));
        printContent = printContent.replace(/{{totalExpense}}/g, totalExpense.toLocaleString('zh-CN', { minimumFractionDigits: 2 }));
        printContent = printContent.replace(/{{finalBalance}}/g, runningBalance.toLocaleString('zh-CN', { minimumFractionDigits: 2 }));

        printWindow.document.write(printContent);
        printWindow.document.close();

        // 等待内容加载完成后打印
        printWindow.onload = function() {
          setTimeout(() => {
            printWindow.print();
          }, 500);
        };

      } else {
        // 没有找到模板
        ElMessage.error('未找到现金日记账打印模板，请联系管理员配置模板');
        return;
      }

    } catch (templateError) {
      console.error('获取打印模板失败:', templateError);
      ElMessage.error('获取打印模板失败，请联系管理员检查模板配置');
      return;
    }

  } catch (error) {
    console.error('打印失败:', error);
    ElMessage.error('打印失败: ' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 分页相关方法
const handleSizeChange = (size) => {
  pageSize.value = size;
  loadTransactions();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  loadTransactions();
};

// 组件挂载时加载数据
onMounted(() => {
  loadTransactions();
});
</script>

<style scoped>
.transactions-container {
  padding: 10px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.stats-section {
  margin-bottom: 20px;
}

.data-card {
  margin-bottom: 20px;
}

.stat-card {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
}

.stat-card.income .stat-icon {
  background: #e8f5e8;
  color: #67c23a;
}

.stat-card.expense .stat-icon {
  background: #fef0f0;
  color: #f56c6c;
}

.stat-card.balance .stat-icon {
  background: #e6f7ff;
  color: #409eff;
}

.stat-card.count .stat-icon {
  background: #f4f4f5;
  color: #909399;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.table-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.amount-income {
  color: #67c23a;
  font-weight: bold;
}

.amount-expense {
  color: #f56c6c;
  font-weight: bold;
}

.dialog-footer {
  text-align: right;
}

.upload-demo {
  text-align: center;
}
</style>
